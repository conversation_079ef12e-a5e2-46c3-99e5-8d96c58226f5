# portal.vanstraaten.test

## Traits
- When to use 'able' or 'has', like: Sluggable or hasSlugs, Pageable or hasPages
  - 'Has' should be applied when its a component or relatable model
  - 'able' when it gives 'shared' features like: models/modules that are pageable or treeable for categories en products  

##Relatables/Morphables
There are Models like (belonging to model): Images & Documents that are more of a standalone system and belong to 1 other Model
There are Models like (standalone):         Tags or Address that can belong to multiple Models 
Howto make a clear difference?
Anwser: 
The first always has 2 moprhable columns: morphable_id, morphabl_type (or use Model name like: imagable_id & imageable_type)
The second should always have a relations table: {{MODELNAME}}_relations
