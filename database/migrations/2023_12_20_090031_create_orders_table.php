<?php

use App\Enums\Order\Type;
use App\Enums\OrderState;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {

            $table->id();
            $table->parentId();
            $table->localeId();
            $table->userId();
            $table->adminId();
            $table->companyId();

            $table->enum('type', Type::values());
            $table->enum('state', OrderState::values())->default(OrderState::DEFAULT);

            $table->string('order_number', 100)->nullable()->default(null);

            $table->string('description', 600)->nullable();
            $table->string('event', 600)->nullable();

            $table->string('currency', 3)->default('EUR');
            $table->decimal('total_items_excl')->nullable();
            $table->decimal('total_express_charges_discount_excl')->nullable();
            $table->decimal('total_express_charges_excl')->nullable();
            $table->decimal('total_energy_excl')->nullable();
            $table->decimal('total_shipping_excl')->nullable();
            $table->decimal('total_discount_excl')->nullable();
            $table->decimal('total_excl')->nullable();
            $table->decimal('total_tax')->nullable();
            $table->decimal('total_incl')->nullable();
            $table->unsignedInteger('total_express_charges_percentage')->nullable();
            $table->unsignedInteger('total_discount_percentage')->nullable();

            $table->is('foreign');
            $table->is('urgent');
            $table->is('reproduction');
            $table->is('combi');
            $table->is('customer');
            $table->is('api');
            $table->is('locked');
            $table->is('paid');
            $table->is('cleaned');

            $table->at('recieved');
            $table->at('order');
            $table->at('sent');

            $table->at('delivery');

            $table->at('production_duedate');
            $table->at('production_synced');

            $table->at('payment_duedate');

                $table->unsignedInteger('reseller_id')->nullable();
                $table->string('reseller_contact_qos_id', 300)->nullable();
                $table->string('reseller_contact_email', 600)->nullable();
            $table->unsignedInteger('discount_reason_id')->nullable();
            $table->unsignedInteger('discount_reason_sub_id')->nullable();
            $table->string('cart_hash', 300)->nullable();

            $table->text('comment')->nullable();
            $table->text('comment_delivery')->nullable();
            $table->text('comment_discount')->nullable();

            $table->is('confirmed');
            $table->is('finished');

            $table->is('onhold');

            $table->timestampsDefault();

            $table->unique(['company_id', 'type', 'order_number'], 'orders_unique');
           // $table->sortColumns();
        });
    }

// $table->string('hash')->nullable();
            // $table->dateTime('quotation_sent_at')->nullable();
            // $table->dateTime('quotation_confirmation_at')->nullable();
            // $table->dateTime('quotation_reminder_sent_at')->nullable();
            // $table->dateTime('order_confirmation_at')->nullable();
            // $table->dateTime('order_reminder_sent_at')->nullable();
            // $table->dateTime('confirmation_sent_at')->nullable();
            // $table->dateTime('preview_sent_at')->nullable();

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
};
