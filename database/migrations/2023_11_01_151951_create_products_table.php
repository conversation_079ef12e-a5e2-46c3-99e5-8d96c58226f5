<?php

use App\Enums\Product\PricingUnit;
use App\Enums\Product\Type;
use App\Enums\Product\TypeSub;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->itemId();
            $table->systemname();
            $table->pageable();

            $table->categoryId();

            $table->string('querytree', 30)->nullable();

            $table->enum('type', array_column(Type::cases(), 'value'))->default(Type::SINGLE);
            $table->enum('type_sub', array_column(TypeSub::cases(), 'value'))->default(TypeSub::PHYSICAL);

            $table->enum('pricing_unit', array_column(PricingUnit::cases(), 'value'))->default(PricingUnit::PIECE);

            $table->boolean('is_pricing_per_sector', 1)->default(0)->unsigned();
            $table->decimal('pricing_start', 10, 5)->nullable()->unsigned();
            $table->decimal('pricing_minimal', 10, 5)->nullable()->unsigned();
            $table->unsignedInteger('pricing_minimal_m2')->nullable();
            $table->unsignedInteger('pricing_minimal_quantity')->nullable();

            $table->in('webshop');
            $table->is('highlight');
            $table->is('printable');
            $table->is('convection');
            $table->sort();

            $table->timestampsDefault();
            $table->softDeletes();
            $table->sortColumns();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
