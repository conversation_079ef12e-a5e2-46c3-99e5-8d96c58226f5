<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::create('heroes', function (Blueprint $table) {

            $table->id();
            $table->systemname();

             $table->title();
             $table->titleSub();
             $table->content();

            $table->string('size', 10)->nullable();

            $table->is('active');
            $table->is('active', true);

            $table->integer('sort')->nullable()->unsigned();

            $table->timestampsDefault();
            $table->sortColumns();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('heroes');
    }
};
