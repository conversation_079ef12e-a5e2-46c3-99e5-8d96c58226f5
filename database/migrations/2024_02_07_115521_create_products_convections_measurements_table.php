<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products_convections_measurements', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('product_convection_id');
            $table->foreign('product_convection_id')
                ->references('id')
                ->on('products_convections')
                ->onDelete('cascade')
                ->onUpdate('no action');

            $table->string('type', 100);
            $table->string('unit', 100)->default('MM1');

            $table->timestampsDefault();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products_convections_measurements');
    }
};
