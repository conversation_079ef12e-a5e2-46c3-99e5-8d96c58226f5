<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->parentId();
            $table->domainId();

            $table->systemname();

            $table->string('hero_header_ids', 100)->nullable();
            $table->string('icon', 50)->nullable();
            $table->string('hero_footer_ids', 100)->nullable();

            $table->pageable();

            $table->in('use');

            $table->is('root');
            $table->is('system');

            $table->is('selectable');
            $table->is('editable');
            $table->is('locked');

            $table->sort();
            $table->sort('menu');
            $table->sort('menu_top');
            $table->sort('menu_1');
            $table->sort('menu_2');
            $table->sort('menu_footer');
            $table->sort('menu_dashboard');

            $table->softDeletes();
            $table->timestampsDefault();
            $table->sortColumns();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pages');
    }
};

//
