<?php

namespace Database\Seeders;

use App\Enums\ItemType;
use App\Enums\ItemUnitTypeSales;
use App\Enums\ItemUnitTypeWarehouse;
use App\Services\APIS\ExactGlobeAPIService;
use DB;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(ConfigSeeder::class);
        $this->call(LocaleSeeder::class);
        $this->call(DomainsTableSeeder::class);

        $this->call(CountrySeeder::class);

        $this->call(CompanySeeder::class);
        $this->call(AdminSeeder::class);

        $this->call(PageSeeder::class);
        $this->call(CategorySeeder::class);

        DB::table('items_groups')->insert($this->getItemGroups());
        DB::table('items')->insert($this->getItems());

        $this->call(AdminSeeder::class);

        $this->call(UserSeeder::class);
        $this->call(PropertySeeder::class);

        $this->call(HeroSeeder::class);
        $this->call(ButtonSeeder::class);
        $this->call(ImageSeeder::class);

        $this->call(ProductSeeder::class);
        $this->call(RoleSeeder::class);
        $this->call(PublicationTypeSeeder::class);

        DB::table('ledgers')->insert([
            [
                'company_id' => 1,
                'systemname' => 'Van Straaten B.V.',
            ],
        ]);

        ExactGlobeAPIService::importLedgers();
    }

    public function getItemGroups()
    {
        return [
            [
                'systemname' => 'substrates',
                'is_active' => 1,
            ],
            [
                'systemname' => 'panels',
                'is_active' => 1,
            ],
            [
                'systemname' => 'stickers',
                'is_active' => 1,
            ],
            [
                'systemname' => 'UV',
                'is_active' => 1,
            ],
            [
                'systemname' => 'DS',
                'is_active' => 1,
            ],
            [
                'systemname' => 'Vinyl',
                'is_active' => 1,
            ],
            [
                'systemname' => 'Printed',
                'is_active' => 1,
            ],
        ];
    }

    public function getItems()
    {
        return [
            [
                'company_id' => 1,
                'item_group_id' => 1,
                'related_item_id' => null,
                'type' => ItemType::DEFAULT,
                'unit_type_warehouse' => ItemUnitTypeWarehouse::ROLL,
                'unit_type_sales' => ItemUnitTypeSales::M2,
                'code' => 'S0165',
                'is_active' => 1,
                'name' => 'Substra556te sd fsdfsdf sdf',
            ],
            [
                'company_id' => 1,
                'item_group_id' => 2,
                'related_item_id' => null,
                'type' => ItemType::DEFAULT,
                'unit_type_warehouse' => ItemUnitTypeWarehouse::ROLL,
                'unit_type_sales' => ItemUnitTypeSales::M2,
                'code' => 'S0019',
                'is_active' => 1,
                'name' => 'Substrate sdsdf s  fsdfsdf sdf',
            ],
            [
                'company_id' => 1,
                'item_group_id' => 3,
                'related_item_id' => null,
                'type' => ItemType::DEFAULT,
                'unit_type_warehouse' => ItemUnitTypeWarehouse::ROLL,
                'unit_type_sales' => ItemUnitTypeSales::M2,
                'code' => 'S0111',
                'is_active' => 1,
                'name' => 'Substdfd7dfrate sdsdf sdf sdf',
            ],
            [
                'company_id' => 1,
                'item_group_id' => 1,
                'related_item_id' => null,
                'type' => ItemType::DEFAULT,
                'unit_type_warehouse' => ItemUnitTypeWarehouse::ROLL,
                'unit_type_sales' => ItemUnitTypeSales::M2,
                'code' => 'S0199',
                'is_active' => 1,
                'name' => 'dsfs f6ffff sd fsdfsdf sdf',
            ],
            [
                'company_id' => 1,
                'item_group_id' => 2,
                'related_item_id' => null,
                'type' => ItemType::DEFAULT,
                'unit_type_warehouse' => ItemUnitTypeWarehouse::ROLL,
                'unit_type_sales' => ItemUnitTypeSales::M2,
                'code' => 'S0250',
                'is_active' => 1,
                'name' => 'aaaaa44aaaaa sdsdf s  fsdfsdf sdf',
            ],
            [
                'company_id' => 1,
                'item_group_id' => 4,
                'related_item_id' => null,
                'type' => ItemType::DEFAULT,
                'unit_type_warehouse' => ItemUnitTypeWarehouse::PIECE,
                'unit_type_sales' => ItemUnitTypeSales::PIECE,
                'code' => 'S0001',
                'is_active' => 1,
                'name' => 'bbbbbbbbbbbfsdfb sdsdf sdf sdf',
            ],
            [
                'company_id' => 1,
                'item_group_id' => 5,
                'related_item_id' => null,
                'type' => ItemType::DEFAULT,
                'unit_type_warehouse' => ItemUnitTypeWarehouse::BOX,
                'unit_type_sales' => ItemUnitTypeSales::PIECE,
                'code' => 'S0003',
                'is_active' => 1,
                'name' => 'bbbbbbbbbghjgjbbb sdsdf sdf sdf',
            ],
            [
                'company_id' => 5,
                'item_group_id' => null,
                'related_item_id' => 2,
                'type' => null,
                'unit_type_warehouse' => ItemUnitTypeWarehouse::ROLL,
                'unit_type_sales' => ItemUnitTypeSales::M1,
                'code' => 'CODE01',
                'is_active' => 1,
                'name' => 'Supplier article-code',
            ],
            [
                'company_id' => 5,
                'item_group_id' => null,
                'related_item_id' => 3,
                'type' => null,
                'unit_type_warehouse' => ItemUnitTypeWarehouse::ROLL,
                'unit_type_sales' => ItemUnitTypeSales::M1,
                'code' => 'CODE02',
                'is_active' => 1,
                'name' => 'Supplier article-code 2',
            ],
        ];
    }
}
