<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'systemname' => 'Management',
            ],
            [
                'systemname' => 'Confection',
            ],
            [
                'systemname' => 'Sales manager',
            ],
            [
                'systemname' => 'Content Manager',
            ],
            [
                'systemname' => 'HR Manager',
            ],
            [
                'systemname' => 'Production',
            ],
            [
                'systemname' => 'Expedition',
            ],
        ];

        foreach ($data as $role) {
            Role::create($role);
        }
    }
}
