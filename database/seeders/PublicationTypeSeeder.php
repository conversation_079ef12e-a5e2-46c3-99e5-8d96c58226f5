<?php

namespace Database\Seeders;

use App\Models\PublicationType;
use Illuminate\Database\Seeder;

class PublicationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'name' => 'notification',
            ],
            [
                'name' => 'publication',
            ],
            [
                'name' => 'announcement',
            ],
        ];

        foreach ($data as $item) {

            PublicationType::create($item);
        }
    }
}
