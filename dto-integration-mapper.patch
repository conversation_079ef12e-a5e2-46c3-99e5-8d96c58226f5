Index: app/IntegrationMapper/ExactProvider.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/IntegrationMapper/ExactProvider.php b/app/IntegrationMapper/ExactProvider.php
new file mode 100644
--- /dev/null	(date 1757057570274)
+++ b/app/IntegrationMapper/ExactProvider.php	(date 1757057570274)
@@ -0,0 +1,34 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\IntegrationMapper;
+
+final class ExactProvider implements ProviderContract
+{
+    private ExactProviderData $data;
+
+    public function fromArray($data): self
+    {
+        $this->data = new ExactProviderData(
+            name: $data['name'],
+            email: $data['email'],
+            phone: $data['phone'],
+            country: $data['country'],
+            city: $data['city'],
+            postalCode: $data['postalCode'],
+            address: $data['address'],
+            debitorNumber: $data['debitorNumber'],
+            creditorNumber: $data['creditorNumber'],
+        );
+
+        return $this;
+    }
+
+    public function toDatabase(): array
+    {
+        return [
+            ...$this->data->toArray(),
+        ];
+    }
+}
Index: app/IntegrationMapper/ExactProviderData.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/IntegrationMapper/ExactProviderData.php b/app/IntegrationMapper/ExactProviderData.php
new file mode 100644
--- /dev/null	(date 1757057570275)
+++ b/app/IntegrationMapper/ExactProviderData.php	(date 1757057570275)
@@ -0,0 +1,44 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\IntegrationMapper;
+
+use Illuminate\Contracts\Support\Arrayable;
+
+final class ExactProviderData implements Arrayable
+{
+    public function __construct(
+        public string $name,
+        public string $email,
+        public string $phone,
+        public string $country,
+        public string $city,
+        public string $postalCode,
+        public string $address,
+        public string $debitorNumber,
+        public string $creditorNumber,
+    ) {
+        //
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'name' => $this->name,
+            'email' => $this->email,
+            'phone' => $this->phone,
+            'country' => $this->country,
+            'city' => $this->city,
+            'postal_code' => $this->postalCode,
+            'address' => $this->address,
+            'debitorNumber' => $this->debitorNumber,
+            'creditorNumber' => $this->creditorNumber,
+        ];
+    }
+
+    public function transform()
+    {
+        return $this;
+    }
+}
Index: app/IntegrationMapper/ProviderContract.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/IntegrationMapper/ProviderContract.php b/app/IntegrationMapper/ProviderContract.php
new file mode 100644
--- /dev/null	(date 1757057570275)
+++ b/app/IntegrationMapper/ProviderContract.php	(date 1757057570275)
@@ -0,0 +1,12 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\IntegrationMapper;
+
+interface ProviderContract
+{
+    public function fromArray($data): self;
+
+    public function toDatabase(): array;
+}
Index: app/IntegrationMapper/ProviderRegistry.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/IntegrationMapper/ProviderRegistry.php b/app/IntegrationMapper/ProviderRegistry.php
new file mode 100644
--- /dev/null	(date 1757057570275)
+++ b/app/IntegrationMapper/ProviderRegistry.php	(date 1757057570275)
@@ -0,0 +1,26 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\IntegrationMapper;
+
+final class ProviderRegistry
+{
+    public private(set) array $providers = [];
+
+    public function __construct()
+    {
+        $this->providers['exact'] = new ExactProvider();
+        $this->providers['exact']['customer'] = new ExactProvider();
+        $this->providers['salesforce'] = new SalesForceProvider();
+    }
+
+    public function getProvider($name): ProviderContract
+    {
+        if (! isset($this->providers[$name])) {
+            throw new \Exception("Provider $name not found.");
+        }
+
+        return $this->providers[$name];
+    }
+}
Index: app/IntegrationMapper/SalesForceProvider.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/IntegrationMapper/SalesForceProvider.php b/app/IntegrationMapper/SalesForceProvider.php
new file mode 100644
--- /dev/null	(date 1757057570275)
+++ b/app/IntegrationMapper/SalesForceProvider.php	(date 1757057570275)
@@ -0,0 +1,18 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\IntegrationMapper;
+
+final class SalesForceProvider implements ProviderContract
+{
+    public function fromArray($data): ProviderContract
+    {
+        return $this;
+    }
+
+    public function toDatabase(): array
+    {
+        return [];
+    }
+}
Index: app/IntegrationMapper/SalesForceProviderData.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/app/IntegrationMapper/SalesForceProviderData.php b/app/IntegrationMapper/SalesForceProviderData.php
new file mode 100644
--- /dev/null	(date 1757057570275)
+++ b/app/IntegrationMapper/SalesForceProviderData.php	(date 1757057570275)
@@ -0,0 +1,15 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\IntegrationMapper;
+
+use Illuminate\Contracts\Support\Arrayable;
+
+final class SalesForceProviderData implements Arrayable
+{
+    public function toArray(): array
+    {
+        return [];
+    }
+}
