<?php

namespace App\View\Components\Admin;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Route;
use Illuminate\View\Component;
use Log;

class But<PERSON> extends Component
{
    public function __construct(
        public ?string $button = null,  // store, back, destroy, create, module
        public ?string $type = null, // submit, button, reset
        public ?string $route = null,
        public ?string $element = null,
        public ?string $label = null,
        public ?string $icon = null,
        public null|string|int|array $parameters = null,
        public bool $disabled = false,
        public ?bool $if = null,
        public string $group = 'top', // top | overview
    ) {

        if ($this->button == 'back') {
            $this->element ??= 'a';
            $this->setRoute('index');
        }

        if ($this->button == 'create') {
            $this->element ??= 'a';
            $this->setRoute($this->button);
        }

        if ($this->button == 'destroy') {
            $this->element ??= 'a';
            $this->setRoute($this->button);
        }

        if ($this->button == 'store') {
            $this->element ??= 'button';
            $this->type ??= 'submit';
            $this->setRoute($this->button);
        }

        if ($this->button == 'edit') {
            $this->element ??= 'a';
            $this->setRoute($this->button);
        }

        if ($this->button == 'module') {
            $this->element ??= 'a';
            $this->setRoute($this->button);
        }

        if (is_null($this->element)) {
            $this->element = 'a';
        }

        if ($this->if === false) {
            $this->disabled = true;
        }

        $this->setLabel();

        $this->setIcon();
    }

    public function setRoute(
        ?string $replacement = null
    ): void {
        if ($this->if === false) {
            $this->route = $this->element == 'a'
                ? '#'
                : null
            ;

            return;
        }

        if (is_string($this->route)) {
            return;
        }

        if (is_null($this->route)) {

            if (! is_null($replacement)) {
                // get current route & replace last segment with index
                $this->route = preg_replace(
                    '/\.[^.]+$/',
                    '.' . ($replacement ?? 'index'),
                    Route::currentRouteName()
                );
            } else {
                $this->route = Route::currentRouteName();
            }
        }

        // check if route exists
        if (! Route::has($this->route)) {
            $this->route = '#';
            Log::error('Route not found compileable Button component', [
                'button' => $this->button,
                'current_route' => Route::currentRouteName(),
            ]);
        }

        // replace route with current route parameters and validate
        try {

            $this->route = route(
                $this->route,
                $this->parameters ?? Route::obtainParametersFromCurrentRoute($this->route)
            );

        } catch (\Exception $e) {

            $this->route = '#';

            Log::error('Route not found compileable Button component', [
                'button' => $this->button,
                'current_route' => Route::currentRouteName(),
                'exception' => $e,
            ]);
        }
    }

    public function setIcon(): void
    {
        if (! is_null($this->icon)) {
            return;
        }

        if ($this->group == 'overview') {

            match ($this->button) {
               // 'store' => $this->icon = 'fa-solid fa-plus',
                'destroy' => $this->icon = 'trash',
                'edit' => $this->icon = 'pen-to-square',
                default => $this->icon = null
            };
        }
    }

    public function setLabel(): void
    {
        if (! is_null($this->label)) {
            return;
        }

        if (! in_array($this->button, [
            'store',
            'back',
            'destroy',
            'create',
            'edit',
        ])) {
            $this->label = null;

            return;
        }

        if ($this->group == 'overview') {
            $this->label = null;

            return;
        }

        $this->label = __('buttons.' . $this->button);
    }

    public function render(): View|string
    {
        // if ($this->if === false) {
        //     return '';
        // }

        return view('admin::components.button');
    }
}
