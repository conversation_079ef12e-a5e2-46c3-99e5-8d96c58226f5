<?php

namespace App\View\Components\Admin\Component\Form;

use App\Services\FormService;
use App\Traits\Blade\HandlesValidationErrors;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Str;

class Toggle extends Component
{
    use HandlesValidationErrors;

    public $checkedReplacementName = null;

    public function __construct(
        public string $name,
        public null|array|Collection|Model $bind = null,
        public ?string $locale = null,
        public mixed $default = null,
        public mixed $checked = null,
        bool $showErrors = true,
        public ?bool $isLocked = null,

        public bool|int|string $col = false,
    ) {

        $this->showErrors = $showErrors;

        $this->name = app(FormService::class)->getInputName($this->name, $this->locale);

        $this->setIsLocked();

        $inputName = Str::bracketsToDots($this->name);

        $boundValue = app(FormService::class)->getBoundValue($this->bind, $inputName);

        if ($this->checked &&
            preg_match('/^(?:\{\{|\[\[)([^=\]\}]+)(?:=([^}\]]+))?(?:\}\}|\]\])$/', $this->checked, $matches)) {
            $this->checkedReplacementName = trim($matches[1]);
            $this->checked = isset($matches[2]) ? trim($matches[2]) : null;

        } else {

            $this->checked = $boundValue ?? $this->checked ?? $this->default ?? false;

            if (! is_bool($this->checked)) {
                $this->checked = in_array($this->checked, ['1', 1, 'true'])
                    ? true
                    : false
                ;
            }

            $this->checked = old($inputName, $this->checked);
        }
    }
}
