<?php

namespace App\View\Components\Admin\Component\Form;

use App\Services\FormService;
use App\Traits\Blade\HandlesValidationErrors;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class Textarea extends Component
{
    use HandlesValidationErrors;

    /*
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(
        public null|array|Collection|Model $bind = null,
        public ?string $locale,
        public string $name,
        public mixed $default = null,
        public bool $floating = false,
        bool $showErrors = true,
        public ?bool $isLocked = null,

        public mixed $value = null,
        public ?string $valueAttribute = null,  // not build in yet I THINK
        public ?string $textEditor = null, // default, mini, markdown

        public bool|int|string $col = false,
    ) {
        $this->showErrors = $showErrors;

        $this->name = app(FormService::class)->getInputName($this->name, $this->locale);

        $this->setIsLocked();

        $this->setValue(
            $this->name,
            $this->bind,
            $this->default
        );
    }
}
