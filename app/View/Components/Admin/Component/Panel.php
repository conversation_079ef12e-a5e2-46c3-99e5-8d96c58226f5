<?php

namespace App\View\Components\Admin\Component;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use Str;

class Panel extends Component
{
    public $isVisible = true;

    /**
     * Create a new component instance.
     */
    public function __construct(
        public $any = null,
        public string $name = '',
        public string $title = 'Panel',

        public ?string $type = null, // locales, overview

        public $withinPanel = true
    ) {
        $this->name = strtolower(empty($this->name)
            ? 'panel-' . Str::slug($this->title)
            : $this->name
        );
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('admin::components.panel.default');
    }
}
