<?php

namespace App\View\Components\Admin\Widget;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class TagsPicker extends Component
{
    public $tags;

    /**
     * Create a new component instance.
     */
    public function __construct(
        public string $column,
        public string $label,
        public $any = null,
        public string $class = '',

        public $value = null,
        public $required = false,
        public $locale = null,
        public $original = null,
        public $properties = [],
        public $toolTip = null
    ) {

        // $this->setData();
        // $this->setLocale();

        // $this->setTags();

        // $this->setLabel();
        // $this->setValue();
        // $this->setOriginal();
        // $this->setProperties();
    }

    private function setTags()
    {
        if (! is_null(old($this->column, null))) {
            $this->tags = json_decode(old($this->column));
        } else {

            $tags = $this->any->tags();

            if ($this->localeId) {
                $tags->where('locale_id', $this->localeId);
            }

            $this->tags = $tags->get()->map(function ($tag) {
                return [
                    'name' => $tag->name,
                    'id' => $tag->id,
                    'locale_id' => $tag->locale_id,
                ];
            })->toArray();
        }
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('admin::components.widgets.tagspicker');
    }
}
