<?php

namespace App\View\Components\Admin\Widget;

use App\Models\Button;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ButtonManager extends Component
{
    public $styles;

    /**
     * Create a new component instance.
     */
    public function __construct(public $any)
    {
        $this->styles = Button::getStyles();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('admin::components.widgets.buttonmanager');
    }
}
