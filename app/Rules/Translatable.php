<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\App;

class Translatable implements DataAwareRule, ValidationRule
{
    protected array $data = [];

    protected bool $required;
    protected array $validLocales;

    public function __construct(
        bool $required = false
    ) {
        $this->required = $required;
        $this->validLocales = array_keys(config('locales', []));
    }

    public function setData(array $data)
    {
        $this->data = $data;

        return $this;
    }

    public function getData()
    {
        return $this->data;
    }

    public function validate(
        $attribute,
        $value,
        Closure $fail
    ): void {
        if (! is_array($value)) {
            $fail("The $attribute must is invalid.");

            return;
        }

        $localeActive = ($this->data['_locale'] ?? App::getLocale());

        // check if $value array keys are in $this->validLocales, if not fail
        if (count($localesInvalid = array_diff(array_keys($value), $this->validLocales)) > 0) {
            $fail("Invalid locale(s) '" . implode("', '", $localesInvalid) . "'.");

            return;
        }

        if ($this->required && (
            is_null($value[$localeActive] ?? null) ||
            trim($value[$localeActive] ?? '') === ''
        )) {
            $fail("You forgot to fill in the " . $attribute . " for " . config('locales.' . $localeActive . '.name') . ".");

            return;
        }
    }
}
