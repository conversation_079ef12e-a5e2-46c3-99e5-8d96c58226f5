<?php

declare(strict_types=1);

namespace App\Services;

use App\Contracts\TransformerContract;
use App\Enums\TransformDirection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Fluent;
use Illuminate\Support\MessageBag;

final readonly class MapValidate
{
    public private(set) MessageBag $errors;

    public function __construct(
        private Fluent             $inputData,
        private array              $mappings = [],
        private TransformDirection $direction = TransformDirection::INBOUND,
    ) {
        $this->errors = new MessageBag();

        Validator::validate($this->mappings, $this->rules());
    }

    private function rules(): array
    {
        return [
            '*' => [
                'required',
                'array',
            ],
            '*.key' => [
                'required',
                'string',
            ],
            '*.rules' => [
                'required',
                'array',
            ],
            '*.before' => [
                'nullable',
                'array',
            ],
            '*.before.*' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    if (! in_array(TransformerContract::class, class_implements($value), true)) {
                        $fail('The before mapping must implement the TransformerContract.');
                    }
                },
            ],
            '*.default' => [
                'nullable',
            ],
        ];
    }

    public function validated(): array
    {
        // preform prepares
        $rulesData = [];
        foreach ($this->mappings as $attribute => $mapping) {

            $key = $mapping['key'] ?? $mapping['key_alternative'] ?? null;

            if (is_null($key)) {
                continue;
            }

            $value = $this->inputData->get($key, $mapping['default'] ?? null);

            if (isset($mapping['before'])) {

                if (! is_array($mapping['before'])) {
                    $mapping['before'] = [$mapping['before']];
                }

                foreach ($mapping['before'] as $before) {

                    if (! is_string($before) ||
                        ! class_exists($before)) {
                        continue;
                    }

                    $before = new $before($this->direction);

                    if (($before instanceof TransformerContract) === false) {
                        continue;
                    }

                    if ($before->canTransform($value)) {
                        $value = $before->transform($value);
                    }
                }
            }

            $value ??= $mapping['default'] ?? null;

            $this->inputData->set($attribute, $value);
            $rulesData[$attribute] = $mapping['rules'];
        }

        return $this->validateAttributes(
            $this->inputData->toArray(),
            $rulesData
        );
    }

    protected function validateAttributes(array $data, array $rules): array
    {
        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            $this->errors->merge($validator->errors());

            return [];
        }

        return $validator->validated();
    }
}
