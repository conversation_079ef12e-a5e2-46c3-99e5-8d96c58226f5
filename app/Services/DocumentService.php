<?php

namespace App\Services;

use App;
use App\Enums\DocumentClassification;
use App\Models\Document;
use App\Services\APIS\GoogleAIPlatformAPIService;
use App\Services\APIS\GoogleDocumentAIAPIService;
use Concurrency;
use Exception;
use Illuminate\Support\Facades\Storage;
use Process;
use Spatie\Browsershot\Browsershot;
use Str;

class DocumentService
{
    public function __construct(
        protected GoogleAIPlatformAPIService $googleAIPlatformAPIService,
        protected GoogleDocumentAIAPIService $googleDocumentAIAPIService
    ) {}

    public function generatePDF(
        int $documentableId,
        string $documentableType,
        DocumentClassification $documentClassification
    ): Document {
        $document = Document::updateOrCreate([
            'documentable_id' => $documentableId,
            'documentable_type' => $documentableType,
            'classification' => $documentClassification,
        ]);

        $versions = $document->versions();

        $version = $versions->exists()
            ? $versions->latest()->first()->version + 1
            : 1
        ;

        $fileName = strtolower($documentableType) . '.pdf';

        $dir = 'documents/' . self::getGroupDirectory($document->id) . '/' . $document->id . '/' . $version . '/';

        // create dir
        Storage::makeDirectory($dir);

        // create PDF
        Browsershot::url(route('pdf.' . strtolower($documentableType), $documentableId))
            ->setOption('args', [
                '--disable-web-security',
                '--run-all-compositor-stages-before-draw',
                '--headless',
            ])
            ->emulateMedia('print')
            ->format('A4')
            ->showBackground()
            ->taggedPdf()
            ->noSandbox()
            ->fullPage()
            ->waitUntilNetworkIdle()
            ->setExtraHttpHeaders([
                'X-Auth-Secret' => config('global.global.auth.header.secret'),
            ])
            ->save(storage_path('app/private/' . $dir . $fileName))
        ;

        if (Storage::missing($dir . $fileName)) {
            throw new Exception($documentableType . ': ' . $documentableId . ' - PDF not generated');
        }

        // check if file is 'big' enough
        if (($fileSize = Storage::size($dir . $fileName)) <= 1) {
            Storage::delete($dir . $fileName);
            throw new Exception($documentableType . ': ' . $documentableId . ' - PDF corrupted');
        }

        $document->versions()->create([
            'name' => $fileName,
            'extension' => 'pdf',
            'mimetype' => 'application/pdf',
            'size' => $fileSize,
            'version' => $version,
        ]);

        return $document;
    }

    public function getClassification(
        Document $document
    ): DocumentClassification {
        return $this->googleAIPlatformAPIService->getDocumentClassification($document);
    }

    public function createData(
        Document $document
    ): bool {
        $data = $this->googleDocumentAIAPIService->getDocumentData($document);

        if ($data === false) {
        return false;
        }

        Storage::put(
            $document->latestVersion->getStoragePath('json'),
            json_encode($data)
        );

        return true;
    }

    public function createImage(Document $document): bool
    {
        $path = $document->latestVersion->getPath();
        $pathOutput = Str::replaceLast('.pdf', '.jpg', $path);

        Process::run(implode(' ', [
            App::isLocal()
                ? 'C:\xampp\htdocs\binaries\poppler\Library\bin\mutool.exe convert'
                : (env('SERVER') == 'code31'
                    ? 'mutool'
                    : '/usr/local/bin/mutool') . ' convert',
            '-o ' . escapeshellarg($pathOutput),
            $path,
        ]));

        $pathOutput = Str::replaceLast('.pdf', '1.jpg', $document->latestVersion->getStoragePath());

        if (Storage::exists($pathOutput)) {
            Storage::move(
                $pathOutput,
                $document->latestVersion->getStoragePath('jpg')
            );
        }

        $pathOutput = $document->latestVersion->getStoragePath('jpg');

        if (Storage::missing($pathOutput) ||
            Storage::size($pathOutput) <= 1024 * 5) {
            return false;
            // throw new Exception('Preview not generated');
        }

        return true;
    }

    public function createHTML(Document $document): bool
    {
        $doc = $document->latestVersion;

        $process = Process::path(public_path())
            ->run(implode(' ', [
                'C:\xampp\htdocs\binaries\poppler\Library\bin\pdf2htmlEX\pdf2htmlEX.exe',
                '--zoom', '1',
                '--hdpi', '300',
                '--vdpi', '300',
                $doc->getPath(),
                getRelativePath(
                    public_path(),
                    $doc->getPath('html')
                ),
            ])
        );

        if (! $process->successful()) {
        return false;
        }

        if (Storage::missing($doc->getStoragePath('html'))) {
        return false;
        }

        if (Storage::size($doc->getStoragePath('html')) <= 1024 * 5) {
        return false;
        }

        return true;
    }

    public function process(
        Document $document
    ) {

        $results = Concurrency::run([

            // function() {

            //     $document = Document::where('id', 27)->with('latestVersion')->first();

            //     $filePreview = storage_path('image.png');

            //     $process = Process::run(implode(' ', [
            //         App::isLocal()
            //             ? 'cmd /C C:\xampp\htdocs\binaries\poppler\Library\bin\mutool.exe convert'
            //             : (env('SERVER') == 'code31'
            //                 ? 'mutool'
            //                 : '/usr/local/bin/mutool').' convert',
            //         '-F png',
            //         '-o '.$filePreview,
            //         $document->latestVersion->getPath(),
            //     ]));

            //     return $process->output();

            // },
            function () use ($document) {

                $class = new GoogleAIPlatformAPIService();

                return $class->getDocumentClassification($document);
            },
            function () use ($document) {

                $class = new GoogleDocumentAIAPIService();

                return $class->getDocumentData($document);
            },

        ]);

        dd($results);

        // $documentClassification = $this->googleAIPlatformAPIService->getDocumentClassification($document);

        // shell_exec(implode(' ', [
        //     App::isLocal()
        //         ? 'cmd /C C:\xampp\htdocs\binaries\poppler\Library\bin\mutool.exe convert'
        //         : (SERVER == 'code31'
        //             ? 'mutool'
        //             : '/usr/local/bin/mutool').' convert',
        //     '-F png',
        //     '-o '.($filePreview = ROOT.'/admin/views/dashboard/documents/'.$pathInfo['filename'].'.png'),
        //     $fileName
        // ]));

        // $documentData = $this->googleDocumentAIAPIService->getDocumentData($document);

        // if (in_array(false, $documentData)) {
        //     return false;
        // }

        // dd($documentData);
    }

    public static function getGroupDirectory($documentId): int
    {
        return floor($documentId / 1000) + 1;
    }

    // public static function getLatestVersion(
    //     int $documentableId,
    //     string $documentableType,
    //     DocumentClassification $documentClassification
    // ) {

    //     return Document::query()
    //         ->where('documentable_id', $documentableId)
    //         ->where('documentable_type', $documentableType)
    //         ->where('type', DocumentClassification::PURCHASE_ORDER)
    //         ->with('lastestVersion')
    //         ->first()
    //     ;
    // }
}
