<?php

namespace App\Services;

use App\Enums\TransformDirection;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Fluent;
use Illuminate\Support\MessageBag;
use stdClass;

class DataMapperService
{
    public MessageBag $errors;

    protected array $validatedData = [];

    public function __construct(
        public array $mappings,
        public array|Arrayable|Collection|Model|stdClass $inputData,
        public TransformDirection $direction = TransformDirection::INBOUND,
    ) {
        if ($this->inputData instanceof Arrayable) {
            $this->inputData = $this->inputData->toArray();
        }

        if ($this->inputData instanceof stdClass) {
            $this->inputData = (array) $this->inputData;
        }

        $this->inputData = Arr::dot($this->inputData);

        $mapper = new MapValidate(new Fluent($this->inputData), $this->mappings, $this->direction);

        $this->validatedData = $mapper->validated();
        $this->errors = $mapper->errors ?? new MessageBag();
    }

    public function validated(): array
    {
        return $this->validatedData;
    }

    public function hasErrors(): bool
    {
        return $this->errors->count() > 0;
    }
}
