<?php

namespace App\Services\APIS;

use App\Enums\TaxType;
use Illuminate\Support\Facades\Cache;
use Spatie\Regex\Regex;

class VIESAPIService extends APIService
{
    public function validate($number, $countryCode = null)
    {
        $number = Regex::replace('/[^A-Z0-9]/im', '', strtoupper($number))->result();

        if (Regex::match('/^[a-zA-Z]{2}/', $number)->hasMatch()) {
            $countryCode = substr($number, 0, 2);
            $number = ltrim($number, $countryCode);
        }

        if (is_null($countryCode)) {
        return false;
        }

        if (! $this->hasCountry($countryCode)) {
        return false;
        }

        $response = $this->request(
            'check-vat-number',
            'post', [
                'body' => json_encode([
                    "countryCode" => $countryCode,
                    "vatNumber" => $number,
                ]),
            ]
        );

        if (($response['valid'] ?? null) == true) {
            return [
                'valid' => true,
                'country_code' => strtoupper($countryCode),
                'number' => $response['countryCode'] . $response['vatNumber'],
                'type' => TaxType::VAT,
            ];
        }

        return false;
    }

    public function hasCountry($countryCode)
    {
        return in_array(
            strtolower($countryCode),
            $this->config['countrycodes']
        );
    }

    // public function isActiveCountry($countryCode)
    // {
    //     return $this->getStatus()[strtoupper($countryCode)] ?? false;
    // }

    // public function getStatus($forceRenew = false): array
    // {
    //     if ($forceRenew) {
    //         Cache::forget($this->storagePathPrefix.'status');
    //     }

    //     return (array) Cache::remember(
    //         $this->storagePathPrefix.'status',
    //         $this->storageExpireIn, function() {

    //             $countries = [];
    //             if (($response = $this->request('check-status')) !== false &&
    //                 isset($response['countries'])) {

    //                 foreach ($response['countries'] as $country) {
    //                     $countries[$country['countryCode']] = $country['availability'] == 'Available'
    //                         ? true
    //                         : false
    //                     ;
    //                 }
    //             }

    //             return $countries;
    //         }
    //     );
    // }
}
