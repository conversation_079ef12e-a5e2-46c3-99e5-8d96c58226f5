<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ModuleController;
use App\Http\Requests\StorePermissionsRequest;
use App\Models\Role;
use App\Models\Settings;
use Illuminate\Contracts\View\View;

class PermissionsController extends ModuleController
{
    public function index(): View
    {
        // $setting = Settings::create([
        //     'systemname' => 'test',
        //     'key' => 'test',
        //     'value' => 'test'
        // ]);
        // $setting = Settings::find(1)->first();

        // $role = Role::whereName('management')->first();
        // $role->settings()->attach($setting->id, [
        //     'value' => 'testfdfdff'
        // ]);

        // dd([Role::whereName('management')->first()->settings()->first()->value,
        // Role::whereName('management')->first()->settings()->first()->defaultValue]);

        return view('admin::pages.permissions.index', [
            'data' => Role::withCount('admins')->get(),
        ]);
    }

    public function create(): View
    {
        return $this->edit(new Role());
    }

    public function edit(Role $permission): View
    {
        $permission->load('relatedSettings');

        return view('admin::pages.permissions.create', [
            'data' => $permission,
            'settings' => Settings::all(),
        ]);
    }

    public function store(StorePermissionsRequest $request): View
    {
        $model = Role::findById($request->validated()['id']) ?? new Role();

        foreach (config('locales') as $locale) {
            $model->{'description_' . $locale['code']} = $request->validated()['description_' . $locale['code']] ?? null;
        }

        $model->name = $request->validated()['name'];
        $model->save();

        $model->relatedSettings()->delete();

        $settings = [];
        foreach ($request->validated()['settings'] as $settingId => $value) {
            $settings[] = [
                'setting_id' => $settingId,
                'value' => $value,
            ];
        }

        $model->relatedSettings()->createMany($settings);

        session()->flash('message', $model->id ? 'Role updated successfully' : 'Role created successfully');

        return self::index();
    }

    public function destroy(Role $data): View
    {
        $data->delete();
        session()->flash('message', 'Role deleted successfully');

        return self::index();
    }
}
