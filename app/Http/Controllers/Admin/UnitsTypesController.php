<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ModuleController;
use App\Http\Requests\StoreUnitTypeRequest;
use App\Models\Unit;
use App\Models\UnitType;
use Illuminate\Contracts\View\View;

class UnitsTypesController extends ModuleController
{
    public function index(): View
    {
        return view('admin::pages.unitstypes.index', [
            'data' => UnitType::query()
                ->with([
                    'baseUnit',
                ])
                ->withCount([
                    'units',
                    'baseUnit',
                ])
                ->applyFilter()
                ->applySort()
                ->withIsLocked()
                ->get(),
        ]);
    }

    public function create(): View
    {
        return $this->edit(new UnitType());
    }

    public function edit(
        UnitType $unitType
    ): View {
        return view('admin::pages.unitstypes.create', [
            'data' => $unitType,
        ]);
    }

    public function store(StoreUnitTypeRequest $request): View
    {
        $model = UnitType::updateOrCreate([
            'id' => $request->input('id'),
        ], $request->validated());

        session()->flash('message', 'Unit type ' . $model->systemname . ' ' . ($model->wasRecentlyCreated
            ? 'updated'
            : 'created'
        )) . ' successfully';

        return self::index();
    }

    public function destroy(UnitType $unittype): View
    {
        $unittype->delete();
        session()->flash('message', 'Unit type deleted successfully');

        return self::index();
    }

    // public function unitsStore(
    //     StoreUnitTypeUnitRequest $request
    // ): View
    // {
    //     $model = Unit::updateOrCreate([
    //         'id' => $request->input('id')
    //     ], $request->validated());

    //     session()->flash('message',  $model->wasRecentlyCreated
    //         ? 'Unit '.$model->systemname.' updated successfully'
    //         : 'Unit '.$model->systemname.' created successfully'
    //     );

    //     return self::units($unittype);
    // }
}
