<?php

namespace App\Http\Controllers\Admin;

use App\Enums\SystemContext;
use App\Http\Controllers\ModuleController;
use App\Models\Company;
use App\Models\Item;
use App\Models\ItemGroup;
use App\Models\Property;
use App\Models\Unit;
use App\Models\UnitGroup;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Str;

class SuppliersItemsController extends ModuleController
{
    public function index(
        ?Company $company
    ): View {
        $items = Item::query()
            ->whereNotNull('items.item_related_id')
            ->withCount('products')
            ->with([
                'company:id,name',
                'related',
                'related.group:id,systemname',
                'related.units',
                'related.units.unitGroupUnit',
                'related.units.unitGroupUnit.unit',
                'units',
                'units.unitGroupUnit',
                'units.unitGroupUnit.unit',
            ])
            ->withIsLocked()
            ->applyFilter()
            ->applySort()
            ->get()
        ;

        return view('admin::pages.suppliersitems.index', [
            'company' => $company,
            'data' => $items,
        ]);
    }

    public function create(
        Company $company
    ): View {
        return $this->edit($company, new Item());
    }

    public function edit(
        Company $company,
        Item $item
    ): View|RedirectResponse {
        $item->load([
            'rules',
            'units',
            'units.relations',
            'units.unitGroupUnit',
            'units.unitGroupUnit.unit',
            'units.unitGroupUnit.unit.compositionals',
            'variants',
            'variants.units',
            'variants.units.unitGroupUnit',
            'variants.units.unitGroupUnit.unit',
            'variants.units.unitGroupUnit.unit.compositionals',
            'variants.units.relations',
            'variants.units.relations.relatable',
            'ledgerAccounts',
        ]);

        $properties = Property::query()
            ->where('system_context', SystemContext::UNIT)
            ->with('values')
            ->get()
        ;

        $unitGroups = UnitGroup::query()
            ->whereNot('value', 'weight')
            ->with([
                'units',
                'units.unit',
                'units.unit.compositionals',
            ])
            ->get()
        ;

        $unitGroupWeightUnits = Unit::query()
            ->whereNot('unit_type_id', 4)
            ->get()
        ;

        $itemsRelated = Item::query()
            ->where('company_id', Company::main())
            ->with([
                'group',
                'units',
                'units.relations',
                'units.unitGroupUnit',
                'units.unitGroupUnit.unit',
                'units.unitGroupUnit.unit.compositionals',
            ])
            ->withIsLocked()
            ->get()
        ;

        $this->bladeService->addToDataSet('item', [$item]);
        $this->bladeService->addToDataSet('properties', $properties);
        $this->bladeService->addToDataSet('unit_groups', $unitGroups);
        $this->bladeService->addToDataSet('itemsRelated', $itemsRelated);

        return view('admin::pages.suppliersitems.create', [
            'data' => $item,
            'company' => $company,
            'companies' => ! $company->exists
                ? Company::query()
                    ->where('is_supplier', true)
                    ->get()
                : null,

            'unitGroups' => $unitGroups,
            'unitGroupWeightUnits' => $unitGroupWeightUnits,
            'properties' => $properties,
            'itemGroups' => ItemGroup::all(),
            'itemsRelated' => $itemsRelated,
        ]);
    }

    public function destroy(
        ?Company $company,
        Item $item
    ): RedirectResponse {
        if ($item->is_locked) {
            $this->setError('The item cannot be deleted.');

            return redirect()->to(url()->previous());
        }

        $companyId = $company?->id ?? $item->company_id;

        $item->delete();

        $this->setMessage($item);

        $previousRoute = app('router')->getRoutes()?->match(request()->create(url()?->previous()))?->getName();

        return Str::endsWith($previousRoute, 'index')
            ? redirect()->to(url()->previous())
            : to_route(preg_replace('/([._])edit$/', '$1index', $previousRoute), $companyId)
        ;
    }
}
