<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ModuleController;
use App\Http\Requests\companies\StoreCompanyRequest;
use App\Models\Company;
use Illuminate\Contracts\View\View;

class AdministrationsController extends ModuleController
{
    public function index(): View
    {
        return (new CompaniesController())->index('administration');
    }

    public function create(): View
    {
        return (new CompaniesController())->edit(new Company());
    }

    public function edit(Company $data): View
    {
        return (new CompaniesController())->edit($data);
    }

    public function store(StoreCompanyRequest $request): View
    {
        return (new CompaniesController())->store($request);
    }

    public function destroy(Company $data): View
    {
        return (new CompaniesController())->destroy($data);
    }
}
