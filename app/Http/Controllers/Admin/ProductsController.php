<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ModuleController;
use App\Http\Requests\products\StoreProductRequest;
use App\Models\Category;
use App\Models\Image;
use App\Models\Product;
use App\Models\Property;
use App\Services\SalesForceService;
use Illuminate\Contracts\View\View;
use SoapClient;

class ProductsController extends ModuleController
{
    protected $salesForceService;

    public function __construct()
    {
        parent::__construct();
        // SalesForceService $salesForceService
        // $this->salesForceService = $salesForceService;
    }

    public function index($filter = null): View
    {

        // $iban = '******************';

        // \Log::debug([
        //     'verify_iban' => verify_iban($iban, false),
        //     'iban_to_human_format' => iban_to_human_format($iban),
        //     'iban_get_parts' => iban_get_parts($iban),
        //     'iban_get_bank_part' => iban_get_bank_part($iban)
        // ]);

        // $client = new SoapClient('https://ssl.ibanrechner.de/soap?wsdl');
        // $result = $client->validate_iban(
        //     '******************',
        //     'code31',
        //     'Code31LALAblabla2025**'
        // );

        // dd($result);

        $data = Product::query()
            // ->withIsLocked()
            ->applyFilter()
           // ->applySort()
            ->get()
        ;

        return view('admin::pages.products.index', [
            'data' => $data,
        ]);
    }

    public function create(): View
    {
        return $this->edit(new Product());
    }

    public function edit(Product $data): View
    {
        $data->load('pricings.steps');

        return view('admin::pages.products.create', [
            'data' => $data,
            'properties' => $properties = Property::with('values')->get(),
            'selectedPropertyValues' => $selectedPropertyValues = $data->propertyValues?->pluck('id')->toArray(),
            'categories' => $categories = Category::getOptionsList(
                Category::getStructuredList(with: 'properties:id'),
                selected: old('categories', $data->categories->pluck('id')->toArray()),
                disabled: $data->id
            ),
        ]);
    }

    public function store(StoreProductRequest $request): View
    {
        $model = Product::findOrNew($request->input('id'));
        $model->fill($request->validated());
        $model->save();

        $model->categories()->detach();
        $model->categories()->attach(array_filter(request()->input('categories')));

        // if ($request->hasFile('image')) {
        //     Image::upload(
        //         $request->file('image'),
        //         $model->id,
        //         $model->getMorphClass(),
        //     );
        // }

        // $savePricing = false;

        // $model->pricings()->delete();
        // if ($request->input('is_printable') == 1) {

        //     foreach (config('modules.products.pricing.sectors', []) as $s) {
        //         foreach (config('modules.products.pricing.groups', []) as $g) {
        //             foreach (config('modules.products.pricing.countries', []) as $lang) {

        //                 $price = null;
        //                 $priceBack = null;
        //                 $minimalM2 = null;
        //                 $minimalQuantity = null;
        //                 $status = 0;

        //                 if (isset($request->input('sectors_switch')[$s['id']][$g['systemname']][$lang['code']])) {

        //                     if (isset($request->input('sectors_pricing')[$s['id']][$g['systemname']][$lang['code']]) &&
        //                         !empty($request->input('sectors_pricing')[$s['id']][$g['systemname']][$lang['code']])) {

        //                         $price = str_replace(',', '.', $request->input('sectors_pricing')[$s['id']][$g['systemname']][$lang['code']]);
        //                         $status = 1;

        //                         if (isset($request->input('sectors_pricing_minimal_m2')[$s['id']][$g['systemname']][$lang['code']]) &&
        //                             !empty($request->input('sectors_pricing_minimal_m2')[$s['id']][$g['systemname']][$lang['code']])) {
        //                             $minimalM2 = intval($request->input('sectors_pricing_minimal_m2')[$s['id']][$g['systemname']][$lang['code']]);
        //                         }

        //                         if (isset($request->input('sectors_pricing_minimal_quantity')[$s['id']][$g['systemname']][$lang['code']]) &&
        //                             !empty($request->input('sectors_pricing_minimal_quantity')[$s['id']][$g['systemname']][$lang['code']])) {
        //                             $minimalQuantity = intval($request->input('sectors_pricing_minimal_quantity')[$s['id']][$g['systemname']][$lang['code']]);
        //                         }

        //                         //TODO querytree
        //                         // if ($_POST['querytree'] == 'panel.substrate' &&
        //                         //     isset($_POST['sectors_pricing_back'][$s['id']][$g['systemname']][$lang]) &&
        //                         //     !empty($_POST['sectors_pricing_back'][$s['id']][$g['systemname']][$lang])) {
        //                         //         $priceBack = str_replace(',', '.', $_POST['sectors_pricing_back'][$s['id']][$g['systemname']][$lang]);
        //                         // }
        //                     }
        //                 }

        //                 $model->pricings()->create([
        //                     'sector_id' => $s['id'],
        //                     'country' => $lang['code'],
        //                     'groupcode' => $g['systemname'],
        //                     'unit' => 'M2',
        //                     'price' => $price,
        //                     'price_back' => $priceBack,
        //                     'minimal_m2' => $minimalM2,
        //                     'minimal_quantity' => $minimalQuantity,
        //                     'status' => $status,
        //                 ]);

        //                 $savePricing = true;

        //             }
        //         }
        //     }

        //     $model->convections()->delete();

        //     if ($request->has('confection')) {

        //         foreach ($request->input('confection') as $k => $v) {

        //             $confection = $model->convections()->create([
        //                 'qos_convection_id' => $k,
        //                 'qos_convection_code' => $v
        //             ]);

        //             if (empty($request->input('measurement_type')) ||
        //                 !isset($request->input('measurement_type')[$v])) {

        //                 continue;
        //             }

        //             foreach($request->input('measurement_type')[$v] as $index => $type) {

        //                 if (empty($request->input('graduated_values_'.$v.'_'.$index))) {
        //                     continue;
        //                 }

        //                 $measurement = $confection->measurements()->create([
        //                     'type' => $type,
        //                     "unit" => 'MM1',
        //                 ]);

        //                 $list = [];
        //                 $lastMin = 0;

        //                 foreach (json_decode($request->input('graduated_values_'.$v.'_'.$index), true) as $step) {
        //                     if ($step['minimal'] > $lastMin &&
        //                         !empty($step['minimal'])) {

        //                         $step['value'] = $step['value'];
        //                         $list[] = $step;
        //                         $lastMin = $step['minimal'] > $lastMin ? $step['minimal'] : $lastMin;
        //                     }
        //                 }

        //                 foreach ($list as $i => $vv) {

        //                     $maximal = 99999;

        //                     if ($vv['minimal'] < $lastMin) {
        //                         $maximal = $list[$i + 1]['minimal'] - 1;
        //                     }

        //                     $step = $measurement->steps()->create([
        //                         'amount_start' => $vv['minimal'],
        //                         'amount_end' => $maximal,
        //                         'value' => $vv['value'],
        //                     ]);
        //                 }
        //             }

        //         }

        //     }
        // }

        // $savePricingConvection = false;

        // if ($request->input('is_convection') == 1) {
        //     $savePricingConvection = true;
        // }

        // if (!$request->input('is_convection') &&
        //     !$request->input('is_printable') == 1) {
        //     $savePricingConvection = true;
        // }

        // if ($savePricingConvection) {

        //     foreach (config('modules.products.pricing.countries', []) as $lang) {

        //         $sectors = config('modules.products.pricing.sectors', []);
        //         foreach ($sectors as $s) {

        //             $sectorId = $s['id'];
        //             if ($request->has('pricing_per_sector') == false) {
        //                 $sectorId = $sectors[0]['id'];
        //             }

        //             if (!$request->has('graduated_values_'.$sectorId.'_'.$lang['code'])) {
        //                 continue;
        //             }

        //             $list = [];
        //             $lastMin = 0;
        //             foreach (json_decode($request->input('graduated_values_'.$sectorId.'_'.$lang['code']), true) as $step) {

        //                 if ($step['minimal'] > $lastMin &&
        //                     !empty($step['minimal'])) {

        //                     $step['value'] = number_format(floatval($step['value']), 4, '.', ',');
        //                     $list[] = $step;
        //                     $lastMin = $step['minimal'];
        //                 }
        //             }

        //             foreach (config('modules.products.pricing.groups', []) as $g) {

        //                 $status = $request->has('pricing_convection_switch_'.$sectorId.'-'.$lang['code'])
        //                     ? 1
        //                     : 0;

        //                 $price = $model->pricings()->create([
        //                     'sector_id' => $sectorId,
        //                     'groupcode' => $g['systemname'],
        //                     'country' => $lang['code'],
        //                     'price' => null,
        //                     'unit' => strtoupper($request->input('pricing_unit')),
        //                     'status' => 0,
        //                 ]);

        //                 $price->save();

        //                 $lowestPrice = 9999999999;
        //                 $hasSteps = false;

        //                 foreach ($list as $i => $v) {

        //                     $maximal = 99999;
        //                     if (isset($list[$i + 1])) {
        //                         $maximal = $list[$i + 1]['minimal'] - 1;
        //                     }

        //                     if ($v['value'] < $lowestPrice) {
        //                         $lowestPrice = $v['value'];
        //                     }

        //                     $price->steps()->create([
        //                         'amount_start' => $v['minimal'],
        //                         'amount_end' => $maximal,
        //                         'price_excl' => $v['value'],
        //                     ]);

        //                     $hasSteps = true;
        //                 }

        //                 if ($hasSteps && $lowestPrice != 9999999999) {

        //                     $price->price = $lowestPrice;
        //                     $price->status = $status;
        //                     $price->save();
        //                 }
        //             }
        //         }
        //     }
        // }

        // if ($request->input('type') == 'end') {

        //     $model->productsSubstratesConvections()->delete();

        //     if ($request->has('substrate') && is_array($request->input('substrate'))) {
        //         foreach ($request->input('substrate') as $s => $id) {

        //             if (($request->input('substrate_convection')) == null ||
        //                 !isset($request->input('substrate_convection')[$s]) ||
        //                 !is_array($request->input('substrate_convection')[$s])) {
        //                 continue;
        //             }

        //             foreach (array_keys($request->input('substrate_convection')[$s]) as $c) {
        //                 $model->productsSubstratesConvections()->create([
        //                     'product_substrate_id' => $id,
        //                     'qos_convection_id' => $c
        //                 ]);
        //             }

        //         }
        //     }
        // }

        session()->flash('message', $model->id ? 'Product updated successfully' : 'Product created successfully');

        return self::index();
    }

    public function destroy(Product $data): View
    {
        $data->delete();
        session()->flash('message', 'Product deleted successfully');

        return self::index();
    }
}
