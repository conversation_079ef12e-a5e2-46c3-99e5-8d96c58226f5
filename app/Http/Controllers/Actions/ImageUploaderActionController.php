<?php

namespace App\Http\Controllers\Actions;

use App\Exceptions\InvalidJsonException;
use App\Http\Controllers\Controller;
use App\Http\Requests\UploadImageUploaderRequest;
use Illuminate\Http\JsonResponse;

class ImageUploaderActionController extends Controller
{
    public function postUpload(
        UploadImageUploaderRequest $request,
        $auth = 'admin'
    ): JsonResponse {

        dd($request->validated());

        if ($company === null) {
            throw new InvalidJsonException(
                "company not found",
                JsonResponse::HTTP_NOT_FOUND
            );
        }

        return response()->json([
            'request' => $request->all(),
            'success' => true,
            'code' => 200,
            'name' => $request->input('dataset', 'default'),
            'count' => 1,
            'index_key' => (new $company())->getKeyName(),
            'data' => [$company->toArray()],
        ]);
    }
}
