<?php

namespace App\Http\Controllers\Actions;

use App\Http\Controllers\Controller;
use App\Http\Requests\PurchaseOrderItemAddRequest;
use Illuminate\Http\JsonResponse;
use Str;

class PurchaseOrderActionController extends Controller
{
    public function postAddItem(
        PurchaseOrderItemAddRequest $request,
        $auth = 'admin'
    ): JsonResponse {
        $data = $request->validated();

        $data['uniq_id'] = is_null($data['uniq_id'] ?? null)
            ? uniqid(Str::random(4), true)
            : $data['uniq_id']
        ;

        return response()->json([
            'success' => true,
            'code' => 200,
            'data' => $data,
        ]);
    }
}
