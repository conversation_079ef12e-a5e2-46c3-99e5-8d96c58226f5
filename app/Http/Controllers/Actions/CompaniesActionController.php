<?php

namespace App\Http\Controllers\Actions;

use App\Exceptions\InvalidJsonException;
use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Item;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CompaniesActionController extends Controller
{
    public function getSupplier(
        Request $request,
        $auth = 'admin'
    ): JsonResponse {
        $filters = json_decode($request->input()['filters'], true);

        $company = Company::query()
            ->where('id', intval(($filters[0] ?? [])['value'] ?? null))
            ->where('is_supplier', 1)
            ->select(['id', 'name', 'country_id', 'locale_id', 'email', 'email_financial', 'phone'])
            ->with([
                'addresses',
                'addresses.address',
                'addresses.address.country:id,code',
                'items',

            ])?->first()
        ;

        return response()->json([
            'request' => $request->all(),
            'success' => ! is_null($company),
            'code' => ! is_null($company)
                ? JsonResponse::HTTP_OK
                : JsonResponse::HTTP_NOT_FOUND,
            'name' => $request->input('dataset', 'default'),
            'count' => 1,
            'index_key' => (new $company())->getKeyName(),
            'data' => is_null($company)
                ? null
                : [$company?->toArray()],
        ]);
    }

    public function getItem(
        Request $request,
        $auth = 'admin'
    ): JsonResponse {
        $filters = json_decode($request->input()['filters'], true);

        $item = Item::query()
            ->where('id', intval(($filters[0] ?? [])['value'] ?? null))
            ->with([
                'units',
                'units.relations',
                'units.unitGroupUnit',
                'units.unitGroupUnit.unit',
                'units.unitGroupUnit.unit.compositionals',

                'variants',
                'variants.units',
                'variants.units.unitGroupUnit',
                'variants.units.unitGroupUnit.unit',
                'variants.units.unitGroupUnit.unit.compositionals',
                'variants.units.relations',
                'variants.units.relations.relatable',
            ])?->first()
        ;

        return response()->json([
            'request' => $request->all(),
            'success' => ! is_null($item),
            'code' => ! is_null($item)
                ? JsonResponse::HTTP_OK
                : JsonResponse::HTTP_NOT_FOUND,
            'name' => $request->input('dataset', 'default'),
            'count' => 1,
            'index_key' => (new $item())->getKeyName(),
            'data' => is_null($item)
                ? null
                : [$item?->toArray()],
        ]);
    }

    public function getAddressLabel(
        Request $request,
        $auth = 'admin'
    ): JsonResponse {

        // if (($company = Company::where('id', intval($request->input()['id']))->with(['addresses', 'country'])?->first()) === null) {
        //     throw new InvalidJsonException(
        //         "company not found",
        //         JsonResponse::HTTP_NOT_FOUND
        //     );
        // }

        // $formatter = new PostalLabelFormatter(
        //     new AddressFormatRepository(),
        //     new CountryRepository(),
        //     new SubdivisionRepository(), [
        //         'locale' => config('locales.'.$company->locale_id.'.id') ?? 'NL'
        // ]);

        // $addresses = [];
        // foreach ($company->addresses as $companyAddress) {

        //     $address = new Address();
        //     $address = $address
        //         ->withOrganization($company->name)
        //         ->withGivenName(trim(implode(' ', [
        //             $companyAddress->firstname,
        //             $companyAddress->lastname,
        //         ])))
        //         ->withCountryCode($company->country->code)
        //         ->withPostalCode(strtoupper($companyAddress->address->postalcode))
        //         ->withLocality(ucwords($companyAddress->address->city))
        //         ->withAddressLine1(ucwords(trim(implode(' ', [
        //             $companyAddress->address->street,
        //             $companyAddress->address->housenumber,
        //             $companyAddress->address->addition
        //         ]))))
        //     ;

        //     $companyAddress->label = $formatter->format($address, [
        //         'origin_country' => $company->country->code,
        //         'html' => true
        //     ]);

        //     $addresses[] = $companyAddress;
        // }

        // return response()->json([
        //     'data' => [
        //         'addresses' => $addresses
        //     ]
        // ]);
        return response()->json([]);
    }
}
