<?php

namespace App\Http\Controllers\Actions;

use App\Http\Controllers\Controller;
use App\Http\Requests\UploadDocumentRequest;
use App\Models\Document;
use App\Services\DocumentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PHPUnit\Util\InvalidJsonException;

class DocumentActionController extends Controller
{
    public function postUpload(
        UploadDocumentRequest $request,
        $auth = 'admin'
    ): JsonResponse {
        $validated = $request->validated();

        $fileName = $validated['file']->getClientOriginalName();

        $fileNameSlugged = Str::slug(pathinfo($fileName)['filename']) . '.' . pathinfo($fileName)['extension'];

        $document = Document::updateOrCreate([
            'documentable_id' => $validated['documentable_id'],
            'documentable_type' => $validated['documentable_type'],
            'classification' => $validated['classification'],
        ]);

        $versions = $document->versions();

        $version = $versions->exists()
            ? $versions->latest()->first()->version + 1
            : 1
        ;

        $document->versions()->create([
            'name' => $fileNameSlugged,
            'name_origin' => $fileName,
            'extension' => pathinfo($fileName, PATHINFO_EXTENSION),
            'mimetype' => $validated['file']->getMimeType(),
            'size' => $validated['file']->getSize(),
            'version' => $version,
        ]);

        $dir = 'documents/' . DocumentService::getGroupDirectory($document->id) . '/' . $document->id . '/' . $version . '/';

        Storage::makeDirectory($dir);

        $validated['file']->storeAs($dir, $fileNameSlugged);

        if (Storage::missing($dir . $fileNameSlugged)) {
            throw new InvalidJsonException(
                "File not uploaded",
                JsonResponse::HTTP_INTERNAL_SERVER_ERROR
            );
        }

        // check if file is 'big' enough
        if (($fileSize = Storage::size($dir . $fileNameSlugged)) <= 1) {

            Storage::delete($dir . $fileNameSlugged);

            throw new InvalidJsonException(
                "File corrupted",
                JsonResponse::HTTP_INTERNAL_SERVER_ERROR
            );
        }

        event(
            'document.uploaded',
            $document
        );

        return response()->json([
            'success' => true,
            'code' => 200,
            // 'data' => [
            //     'supplier' => $company->toArray()
            // ]
        ]);
    }
}
