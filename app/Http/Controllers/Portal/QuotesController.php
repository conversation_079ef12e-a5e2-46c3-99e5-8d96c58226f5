<?php

namespace App\Http\Controllers\Portal;

use App\Http\Controllers\ModuleController;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\URL;

class QuotesController extends ModuleController
{
    public function index(): View
    {
        $data = [
            [
                'id' => 111764,
                'title' => 'dfdsfd LVT',
                'amount' => '€ 127,72',
                'start_date' => '16-11-2023',
                'end_date' => '30-11-2023',
                'status' => 'Waiting for approval',
            ],
            [
                'id' => 111444,
                'title' => 'Order 2',
                'amount' => '€ 12,72',
                'start_date' => '15-11-2013',
                'end_date' => '15-11-2015',
                'status' => 'Ready to ship',
            ],
        ];

        return view('portal.pages.quotes.index', compact('data'));
    }

    public function specific(): View
    {
        $data = [
            'id' => 111764,
            'title' => 'Silicone edge graphic Configurator',
            'price_per_item' => '€ 127,72',
            'start_date' => '16-11-2023',
            'end_date' => '30-11-2023',
            'status' => '123 x 321 cm',
            'image' => URL::asset('/img/silicon-edge.jpg'),
            'reference' => 'fddsfsdf',
            'size' => 'fddsfsdf',
            'material' => 'DecoTex 500',
            'confection' => 'All around slimframe tendon',
            'printed' => 'Printed',
            'countour_cutting' => 'Yes',
            'packaging' => 'On a roll',
            'cutpaths' => true,
            'numbers' => '1',
            'items' => [
                'Frames' => [
                    'profile' => 'Double 49 (Black), Open',
                    'type' => 'Hanging',
                    'max_cutsize' => '4000mm',
                    'stabilizers' => 'Yes',
                    'Number' => '1',
                    'Price per item' => '€ 316,66',
                ],
                'Accessoires' => [
                    'suspension_cables' => '€ 29,68',

                ],
            ],
        ];

        return view('portal.pages.quotes.specific', compact('data'));
    }
}
