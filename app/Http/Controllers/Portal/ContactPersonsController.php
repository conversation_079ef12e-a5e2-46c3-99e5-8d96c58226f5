<?php

namespace App\Http\Controllers\Portal;

use App\Http\Controllers\ModuleController;
use Illuminate\Contracts\View\View;

class ContactPersonsController extends ModuleController
{
    public function index(): View
    {
        $data = [
            [
                'name' => '<PERSON><PERSON>',
                'function' => 'Developer',
            ],
            [
                'name' => '<PERSON>',
                'function' => 'Developer',
            ],
        ];

        return view('portal.pages.accounts.contactpersons.index', compact('data'));
    }

    public function edit(): View
    {

        return view('portal.pages.accounts.contactpersons.edit');
    }
}
