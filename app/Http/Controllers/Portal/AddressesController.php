<?php

namespace App\Http\Controllers\Portal;

use App\Http\Controllers\ModuleController;
use Illuminate\Contracts\View\View;

class AddressesController extends ModuleController
{
    //

    public function index(): View
    {
        $data = [
            [
                'firstname' => 'Freek',
                'lastname' => 'Kamans',
                'company' => 'Code 31 B.V.',
                'street' => 'De V<PERSON> van Steenwijklaan',
                'housenumber' => '75',
                'zipcode' => '7902 NP',
                'city' => 'Hoogeveen',
                'country' => 'Nederland',
                'phonenumber' => '***********',
                'email' => '<EMAIL>',

            ],
            [
                'firstname' => 'Freek',
                'lastname' => 'Kamans',
                'company' => 'Code 31 B.V.',
                'street' => 'De V<PERSON> van Steenwijklaan',
                'housenumber' => '75',
                'zipcode' => '7902 NP',
                'city' => 'Hoogeveen',
                'country' => 'Nederland',
                'phonenumber' => '***********',
                'email' => '<EMAIL>',

            ],
            [
                'firstname' => 'Freek',
                'lastname' => 'Kamans',
                'company' => 'Code 31 B.V.',
                'street' => 'De Vos van Steenwijklaan',
                'housenumber' => '75',
                'zipcode' => '7902 NP',
                'city' => 'Hoogeveen',
                'country' => 'Nederland',
                'phonenumber' => '***********',
                'email' => '<EMAIL>',

            ],
        ];

        return view('portal.pages.accounts.addressbook.index', compact('data'));
    }

    public function edit(): View
    {
        return view('portal.pages.accounts.addressbook.edit');
    }
}
