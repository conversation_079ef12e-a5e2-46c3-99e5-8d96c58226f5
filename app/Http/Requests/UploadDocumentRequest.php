<?php

namespace App\Http\Requests;

use App\Enums\DocumentClassification;
use App\Rules\MimeType;
use App\Rules\ModelHasDocuments;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UploadDocumentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void {}

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'classification' => [
                'nullable',
                new Enum(DocumentClassification::class),
            ],
            'documentable_type' => [
                'nullable',
                new ModelHasDocuments(),
            ],
            'documentable_id' => [
                'nullable',
            ],
            'file' => [
                'required',
                'file',
                new MimeType('document.text'),
                'min:1',          // Minimum size of 1 KB
                'max:10485760',   // Maximum size of 10 GB (10,485,760 KB)
            ],
        ];

        if ($this->input('documentable_type') !== null) {
            $rules['documentable_id'][] = 'exists:App\\Models\\' . $this->input('documentable_type') . ',id';
        }

        return $rules;
    }
}
