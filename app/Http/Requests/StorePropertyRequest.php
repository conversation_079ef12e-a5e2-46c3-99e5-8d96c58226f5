<?php

namespace App\Http\Requests;

use App\Enums\Property\Sorting;
use App\Enums\SystemContext;
use App\Enums\UnitTypeMode;
use App\Models\Property;
use App\Models\Unit;
use App\Models\UnitType;
use App\Rules\Translatable;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePropertyRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'id' => [
                'nullable',
                'integer',
                'exists:properties,id',
            ],
            'systemname' => [
                'required',
                'string',
                'min:2',
                'max:256',
                Rule::unique('properties', 'systemname')->ignore($this->input('id')),
            ],
            'system_context' => [
                'required',
                Rule::enum(SystemContext::class),
            ],
            'system_context_module' => [
                'nullable',
                'string',
                'required_if:system_context,' . SystemContext::MODULE->value,
                // 'exists:modules,id'
            ],
            'system_context_unit_id' => [
                'nullable',
                'integer',
                'required_if:system_context,' . SystemContext::UNIT->value,
                'exists:units,id',
                function ($attribute, $value, $fail) {

                    $unit = Unit::query()
                        ->where('id', intval($value))
                        ->first()
                    ;

                    if ($unit?->type?->mode === UnitTypeMode::COMPOSITIONAL) {
                        return;
                    }

                    if (($property = Property::query()
                        ->where('system_context', SystemContext::UNIT)
                        ->where('system_context_unit_id', intval($value))
                        ->where('system_context_unit_compositional_id', null)
                        ->where('system_context_unit_compositional_unit_id', null)
                        ->select('id', 'systemname')
                        ->first()) !== null) {

                        $fail('The selected unit is already defined in property ' . $property->systemname . '.');

                        return;
                    }
                },
            ],
            'system_context_unit_compositional_id' => [
                'nullable',
                'integer',
                Rule::requiredIf(
                    function () {
                        return Unit::query()
                            ->where('id', intval($this->input('system_context_unit_id')))
                            ->first()?->type?->mode === UnitTypeMode::COMPOSITIONAL
                        ;
                    }
                ),
                Rule::exists('units_compositionals', 'id')->where(function ($query) {
                    $query->where('unit_id', $this->input('system_context_unit_id'));
                }),
            ],
            'system_context_unit_compositional_unit_id' => [
                'nullable',
                'integer',
                'required_with:system_context_unit_compositional_id',
                Rule::exists('units', 'id')->where(function ($query) {
                    $query->whereIn(
                        'unit_type_id',
                        UnitType::query()
                            ->where('mode', UnitTypeMode::CONVERSABLE)
                            ->pluck('id')->toArray()
                    );
                }),
                Rule::unique('properties', 'system_context_unit_compositional_unit_id')
                    ->where(function ($query) {
                        $query
                            ->where('system_context', SystemContext::UNIT)
                            ->where('system_context_unit_id', intval($this->input('system_context_unit_id')))
                            ->where('system_context_unit_compositional_id', intval($this->input('system_context_unit_compositional_id')));
                    })
                    ->ignore($this->input('id')),
            ],
            'sorting' => [
                'required',
                Rule::enum(Sorting::class),
            ],
            'is_filter' => 'boolean',
            'is_sortable' => 'boolean',
            'is_multiple' => 'boolean',
            'is_global' => 'boolean',
            'categories' => [
                'array',
                'nullable',
                'required_if_all:system_context=module,system_context_module=products,is_global=0',
            ],
            'name' => new Translatable(true),
            'content' => new Translatable(false),
        ];
    }

    /**
     * Get the custom error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'systemname.required' => 'The system name is required.',
            'sorting.required' => 'The sorting is required.',
            'systemname.string' => 'The system name must be a string.',
            'systemname.min' => 'The system name must be at least :min characters.',
            'systemname.max' => 'The system name must not exceed :max characters.',
            'systemname.unique' => 'The system name is already taken.',
            'status.boolean' => 'The status field must be a boolean.',
            'is_highlighted.boolean' => 'The is_highlighted field must be a boolean.',
            'published_at.required' => 'The published at field is required.',
            'published_at.date' => 'The published at field must be a valid date.',
            'expired_at.required' => 'The expired at field is required.',
            'expired_at.date' => 'The expired at field must be a valid date.',
            'type_id.required' => 'The type field is required.',
            'type_id.string' => 'The type field must be a string.',
            'status_nl.boolean' => 'The status_nl field must be a boolean.',
            'status_de.boolean' => 'The status_de field must be a boolean.',
            'status_en.boolean' => 'The status_en field must be a boolean.',
            'name_nl.required' => 'The title (NL) field is required when status_nl is true.',
            'name_de.required' => 'The title (DE) field is required when status_de is true.',
            'name_en.required' => 'The title (EN) field is required when status_en is true.',
            'categories.required' => 'Categories are required if you dont check globally available.',

        ];
    }
}
