<?php

namespace App\Traits;

use App\Models\Property;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasProperties
{
    public function properties(): MorphToMany
    {
        return $this->morphToMany(
            Property::class,
            'relatable',
            'properties_relations',
            'relatable_id',
            'property_id'
        );
    }

    // public function properties(): BelongsToMany
    // {
    //     return $this->belongsToMany(
    //         Property::class,
    //         'properties_relations',
    //         'relatable_id',
    //         'property_id')->wherePivot('relatable_type', '=', 'Category');

    // }
}
