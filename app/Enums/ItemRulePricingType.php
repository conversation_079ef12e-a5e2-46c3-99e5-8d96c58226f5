<?php

namespace App\Enums;

use App\Traits\Enum;

enum ItemRulePricingType: string
{
    use Enum;

    const DEFAULT = self::PRICE_FIXED;

    case PRICE_FIXED = 'price_fixed';
    case PRICE_SUBSTRACT = 'price_substract';
    case PRICE_ADD = 'price_add';

    case PERCENTAGE_SUBSTRACT = 'percentage_substract';
    case PERCENTAGE_ADD = 'percentage_add';

    // 'price_fixed' => 'Fixed price',
    // 'price_substract' => 'Price discount',
    // 'price_add' => 'Price increase',
    // 'percentage_add' => 'Percentage discount',
    // 'percentage_substract' => 'Percentage increase'
}
