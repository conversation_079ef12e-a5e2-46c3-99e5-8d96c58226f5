<?php

declare(strict_types=1);

namespace App\Actions\Mapper;

use App\Enums\TransformDirection;
use App\Rules\NotExists;
use App\Services\APIS\ExactGlobeAPIService;
use App\Services\DataMapperService;
use App\Transformers\CountryCodeToCountryIdTransformer;
use App\Transformers\CountryCodeToLocaleIdTransformer;

final class ImportMapperAction
{
    public function __invoke(string $uniqId, TransformDirection $direction): DataMapperService
    {
        $account = new ExactGlobeAPIService()
            ->getAccount($uniqId);

        return new DataMapperService(
            $this->mappings(),
            $account,
            $direction,
        );
    }

    private function mappings(): array
    {
        return [
            'exact_uniq_id' => [
                'key' => 'exact_uniq_id',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'exact_uniq_id'),
                ],
            ],
            'country_id' => [
                'key' => 'country_code',
                'rules' => [
                    'required',
                    'integer',
                    'exists:countries,id',
                ],
                'before' => [
                    CountryCodeToCountryIdTransformer::class,
                ],
            ],
            'locale_id' => [
                'key' => 'country_code',
                'rules' => [
                    'required',
                    'integer',
                    'exists:locales,id',
                ],
                'before' => [
                    CountryCodeToLocaleIdTransformer::class,
                ],
                'default' => 1,
            ],
            'name' => [
                'key' => 'name',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'name'),
                ],
            ],
            'email' => [
                'key' => 'email',
                'rules' => [
                    'nullable',
                    'email:rfc,dns,spoof',
                ],
            ],
        ];
    }
}
