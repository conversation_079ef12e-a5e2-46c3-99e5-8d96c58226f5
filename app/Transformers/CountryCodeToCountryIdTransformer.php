<?php

namespace App\Transformers;

use App\Models\Country;

class CountryCodeToCountryIdTransformer extends Transformer
{
    public function canTransform($value): bool
    {
        return is_string($value);
    }

    public function transform($value): ?string
    {
        if (! $this->canTransform($value)) {
            return null;
        }

        $value = trim($value);

        $attribute = strlen($value) === 2
            ? 'code'
            : 'iso3'
        ;

        return Country::query()
            ->where($attribute, strtoupper(trim($value)))
            ?->first()?->id
        ;
    }

    public function reverseTransform($value): void
    {
        //
    }
}
