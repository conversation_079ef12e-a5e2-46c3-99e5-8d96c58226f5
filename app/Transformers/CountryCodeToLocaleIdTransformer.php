<?php

namespace App\Transformers;

use App\Models\Country;

class CountryCodeToLocaleIdTransformer extends Transformer
{
    public function canTransform($value): bool
    {
        return $this->direction->isInbound() && is_string($value);
    }

    public function transform($value): ?string
    {
        if (! $this->canTransform($value)) {
            return null;
        }

        $value = trim($value);

        if (strlen($value) === 3) {
            $value = Country::query()
                ->where('iso3', strtoupper($value))
                ?->first()?->code;

        }

        if (strlen($value) === 2) {
            return collect(config('locales.' . $value, []))
                ->ensure('string')
                ->get('id');
        }

        return null;
    }

    public function reverseTransform($value): void
    {
        //
    }
}
