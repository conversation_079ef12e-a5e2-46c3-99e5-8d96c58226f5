<?php

namespace App\Console\Commands;

use App\Actions\Mapper\ImportMapperAction;
use App\Enums\TransformDirection;
use Illuminate\Console\Command;

class TestCommand extends Command
{
    protected $signature = 'app:test';

    public function handle(ImportMapperAction $importMapperAction): void
    {
        $mapped = $importMapperAction(
            uniqId: 'C7E48EB5-7470-4D3D-B291-059905DECF87',
            direction: TransformDirection::INBOUND,
        );

        dump($mapped->validated());

        expect($mapped->validated())->toMatchArray([
            'exact_uniq_id' => 'C7E48EB5-7470-4D3D-B291-059905DECF87',
            'country_id' => 152,
            'locale_id' => 1,
            'name' => 'Autoschade Pijnaker',
            'email' => null,
        ]);
    }
}
