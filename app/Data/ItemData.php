<?php

namespace App\Data;

use App\Enums\ItemType;
use App\Enums\LedgerAccountType;
use App\Models\Company;
use App\Rules\NotExists;
use Illuminate\Validation\Rule;
use Spatie\LaravelData\Data;

class ItemData extends Data
{
    public function __construct(
        public ?int $id,
        public int $item_group_id,
        public ?int $item_related_id,
        public int $company_id,
        public string $code,
        public string $name,
        public ItemType $type,
        // public ?bool $is_custom

    ) {}

    public function rules(): array
    {
        $rules = [
            'id' => 'integer|nullable',
            'company_id' => 'required|exists:companies,id',
            'item_group_id' => [
                'required_if:company_id,' . Company::main(),
                'exists:items_groups,id',
            ],
            'item_related_id' => [
                'required_if_not:company_id,' . Company::main(),
                Rule::exists('items', 'id')->where(function ($query) {
                    $query->where('company_id', Company::main());
                }),
            ],
            'code' => [
                'required',
                'string',
                'min:3',
                'max:256',
                new NotExists('items', 'code', [
                    ['company_id', '=', 'company_id'],
                    ['id', '!=', 'id'],
                ]),
            ],
            'name' => 'string|nullable|min:3|max:600',
            'type' => [
                'required_if:company_id,' . Company::main(),
                Rule::enum(ItemType::class),
            ],

            'unit.*.group_id' => 'required|integer|exists:units_groups,id',
            'unit.*.unit_id' => 'required|integer|exists:units,id',
            'unit.*.relations' => 'nullable|array',
            'unit.*.relations.*' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {

                    // Split the value into Model and ID
                    [$model, $id] = explode('::', $value) + [null, null];

                    if (! in_array($model, ['PropertyValue', 'Unit']) || ! ctype_digit($id)) {
                        return $fail("The {$attribute} field contains an invalid relation.");
                    }

                    // Validate if the model exists
                    $class = "App\\Models\\{$model}";
                    if (! class_exists($class) ||
                        ! (new $class())->where('id', $id)->exists()) {
                        return $fail("The {$attribute} field references a non-existent {$model}.");
                    }
                },
            ],

            'ledger_account.*.type' => [
                'string',
                'nullable',
                Rule::enum(LedgerAccountType::class),
            ],
            'ledger_account.*.id' => 'integer|nullable|exists:ledgers_accounts,id',
        ];

        return $rules;
    }
}
