<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\MorphMany;

class Usp extends BaseModel
{
    protected $fillable = [
        'systemname',
        'icon',
        'title_en',
        'title_nl',
        'title_de',
        'title_sub_en',
        'title_sub_nl',
        'title_sub_de',
        'text_en',
        'text_nl',
        'text_de',
        'is_menu_top',
        'is_footer',
        'status',
        'status_de',
        'status_en',
        'status_nl',
        'sort',
    ];

    protected $casts = [
        'id' => 'integer',
        'systemname' => 'string',
        'icon' => 'string',
        'title_en' => 'string',
        'title_nl' => 'string',
        'title_de' => 'string',
        'title_sub_en' => 'string',
        'title_sub_nl' => 'string',
        'title_sub_de' => 'string',
        'text_en' => 'string',
        'text_nl' => 'string',
        'text_de' => 'string',
        'is_menu_top' => 'boolean',
        'is_footer' => 'boolean',
        'status' => 'boolean',
        'status_de' => 'boolean',
        'status_en' => 'boolean',
        'status_nl' => 'boolean',
        'sort' => 'integer',
        'updated_at' => 'datetime:d-m-Y H:i:s',
        'created_at' => 'datetime:d-m-Y H:i:s',
    ];

    public function linkable(): MorphMany
    {
        return $this->MorphMany(Attachment::class, 'linkable');
    }
}
