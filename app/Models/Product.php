<?php

namespace App\Models;

use App\Enums\Product\PricingUnit;
use App\Enums\Product\Type;
use App\Enums\Product\TypeSub;
use App\Models\Filters\ProductCategoriesFilter;
use App\Models\Filters\ProductTypeFilter;
use App\Traits\GetStatusDot;
use App\Traits\HasAttachments;
use App\Traits\HasPropertyValues;
use App\Traits\HasTags;
use App\Traits\HasTranslations;
use App\Traits\IsFilterable;
use App\Traits\Pageable;
use App\Traits\Sluggable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Product extends BaseModel
{
    use GetStatusDot,
        HasAttachments,
        HasPropertyValues,
        HasTags,

        HasTranslations,

        IsFilterable,
        Pageable,
        Sluggable;

    protected $fillable = [
        'category_id',

        'systemname',
        'type',
        'type_sub',

        'pricing_unit',
        'pricing_start',
        'pricing_minimal',
        'pricing_minimal_m2',
        'pricing_minimal_quantity',

        'in_webshop',
        'is_highlight',
        'is_convection',
        'is_printable',
        'is_pricing_per_sector',
        // 'sort',
    ];

    protected $casts = [
        'type' => Type::class,
        'type_sub' => TypeSub::class,
        'pricing_unit' => PricingUnit::class,

        'pricing_start' => 'decimal:2',
        'pricing_minimal' => 'decimal:2',
        'pricing_minimal_m2' => 'decimal:2',
        'pricing_minimal_quantity' => 'decimal:2',
      //  'sort' => 'integer'
    ];

    protected array $filters = [
        ProductTypeFilter::class,
        ProductCategoriesFilter::class,
    ];

    public function item(): BelongsTo
    {
        return $this->belongsTo(Item::class, 'item_id');
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(
            Category::class,
            'products_categories',
            'product_id',
            'category_id'
        );
    }

    // public static function get(array $args = []): Collection | Product | false
    // {

    //     $cfg = array_merge([
    //         'id' => null,
    //         'get_properties' => false,
    //         'get_category_ids' => false,
    //         'get_category_names' => false,
    //         'get_convections' => false,
    //         'get_convections_measurements' => false,
    //         'count_substrates_convections' => false,
    //         'get_language_titles' => false,
    //         'get_pricing' => false,
    //         'has_pricing' => false,
    //         'count_measurements' => false,
    //         'get_categories' => false,
    //         'count_pricing' => false,
    //         'get_language_columns' => false,
    //         'get_language_titles' => false,
    //         'in_webshop' => false,
    //         'is_printable' => false,
    //         'is_convection' => false,
    //         'is_highlight' => false,
    //     ], $args);

    //     if (!isset($cfg['session'])) {
    //         $cfg['session'] = session()->all();
    //     }

    //     $queryBuilder = Product::query();

    //     if (!is_null($cfg['id'])) {
    //         $queryBuilder->where('products.id', $cfg['id']);
    //     }

    //     if (isset($cfg['ids'])) {
    //         $queryBuilder->whereIn('products.id', $cfg['ids']);
    //     }

    //     foreach (['in_webshop', 'is_printable', 'is_convection', 'is_highlight'] as $key) {
    //         if ($cfg[$key]) {
    //             $value = $cfg[$key];
    //             $queryBuilder->where($key, $value);
    //         }
    //     }

    //     if (isset($cfg['qos_convection_code'])) {
    //         $queryBuilder->where('products.qos_convection_code', $cfg['qos_convection_code']);
    //     }

    //     if (isset($cfg['qos_substrate_code'])) {
    //         $queryBuilder->where('products.qos_substrate_code', $cfg['qos_substrate_code']);
    //     }

    //     if(isset($cfg['category_id'])){
    //         $queryBuilder->whereHas('categories', function (Builder $query) use ($cfg) {
    //             $query->where('categories.id', $cfg['category_id']);
    //         });
    //     }

    //     if (isset($cfg['substrate_codes'])) {
    //         $queryBuilder->whereIn('products.qos_substrate_code', $cfg['substrate_codes']);
    //     }

    //     if (isset($cfg['status'])) {
    //         $queryBuilder->where([
    //             'status'=> $cfg['status'],
    //             'status_'.App::getLocale() => $cfg['status']
    //         ]);
    //     }

    //     if(isset($cfg['type'])) {
    //         if (!is_array($cfg['type'])) {
    //             $queryBuilder->where('products.type', $cfg['type']);
    //         } else {
    //             $queryBuilder->whereIn('products.type', $cfg['type']);
    //         }
    //     }

    //     if ($cfg['get_categories']) {
    //         $queryBuilder->with('categories');
    //     }

    //     if ($cfg['get_properties']) {
    //         $queryBuilder->with('propertyValues');
    //     }

    //     if ($cfg['get_convections']) {
    //         $queryBuilder->with('convections');
    //     }

    //     if ($cfg['get_convections_measurements']) {
    //         $queryBuilder->with('measurements');
    //     }

    //     if ($cfg['get_category_ids']) {
    //         $queryBuilder->with(['categories:id']);
    //     }

    //     if ($cfg['get_category_names']) {
    //         $queryBuilder->with(['categories:systemname']);
    //     }

    //     if ($cfg['get_pricing']) {
    //         $queryBuilder->with('pricings.steps');
    //     }

    //     $selects = [
    //         'products.id',
    //         'products.qos_substrate_id',
    //         'products.qos_convection_id',
    //         'products.qos_convection_code',
    //         'products.qos_substrate_code',
    //         'products.category_id',
    //         'products.systemname',
    //         'products.type',
    //         'products.status',
    //         'products.is_printable',
    //         'products.is_convection',
    //         'products.is_highlight',
    //         'products.in_webshop',
    //         'products.querytree',
    //         'products.pricing_per_sector',
    //         'products.pricing_start',
    //         'products.pricing_minimal',
    //         'products.pricing_minimal_m2',
    //         'products.pricing_minimal_quantity',
    //     ];

    //     $columnsTitles = [
    //         'products.title',
    //         'products.title_tab',
    //         'products.title_configurator',
    //     ];

    //     $columnsLanguage = array_merge($columnsTitles, [
    //         'products.content',
    //         'products.status',
    //         'products.title_sub',
    //     ]);

    //     if ($cfg['get_language_columns']) {
    //         $selects[] = Language::createColumns($columnsLanguage, 'select');
    //     }

    //     if ($cfg['get_language_titles']) {
    //         $selects[] = Language::createColumns($columnsTitles, 'select');
    //     }

    //     if ($cfg['has_pricing']) {

    //         $queryBuilder
    //         ->leftJoin('products_pricing', 'products.id', '=', 'products_pricing.product_id')
    //         ->where([
    //             'products_pricing.sector_id' => $cfg['session']['company']->sector_id,
    //             'products_pricing.groupcode' => $cfg['session']['company']->groupcode,
    //             'products_pricing.country' => $cfg['session']['company']->country->code
    //         ]);

    //         $selects[] = [
    //             'products_pricing.price',
    //         ];

    //     }

    //     $queryBuilder->select(Arr::flatten($selects));

    //     if ($cfg['count_pricing']) {
    //         $queryBuilder->withCount('pricings');
    //     }

    //     if ($cfg['count_substrates_convections']) {
    //         $queryBuilder->withCount('productsSubstratesConvections');
    //     }

    //     //TODO count measurements + steps
    //     if ($cfg['count_measurements']) {
    //         $queryBuilder->withCount('measurements');
    //     }

    //     $orderByColumn = 'systemname';
    //     $orderByOrder = 'ASC';

    // 	if (isset($cfg['order_by']) && is_array($cfg['order_by'])) {
    //         $orderByColumn = $cfg['order_by'][0];
    //         $orderByOrder = $cfg['order_by'][1];
    // 	}

    //     $queryBuilder->orderBy($orderByColumn , $orderByOrder);

    //     $result = $queryBuilder->get();

    //     if (!is_null($cfg['id']) ||
    //         isset($cfg['qos_substrate_code']) ||
    //         isset($cfg['qos_convection_code']) ||
    //         isset($cfg['qos_substrate_id']) ||
    //         isset($cfg['qos_convection_id'])
    //     ) {
    //         return count($result) > 0
    //             ? $result->first()
    //             : false;
    //     }

    //     return $result;
    // }

    public static function getPricing($args = []): Collection|false
    {
        $queryBuilder = ProductPrice::query();

        $queryBuilder->where('product_id', $args['product_id']);

        if (($args['country'] ?? false) !== false) {
            $queryBuilder->where('country', $args['country']);
        }

        if (($args['sector_id'] ?? false) !== false) {
            $queryBuilder->where('sector_id', $args['sector_id']);
        }

        if (($args['groupcode'] ?? false) !== false) {
            $queryBuilder->where('groupcode', $args['groupcode']);
        }

        if (($args['status'] ?? false) !== false) {
            $queryBuilder->where('status', $args['status']);
        }

        if (! isset($args['has_price'])) {
            $queryBuilder->where('price', '!=', 0);
        }

        $queryBuilder->with('steps');

        $list = $queryBuilder->get();

        if (count($list) == 0) {
            return false;
        }

        return $list;
    }

//     select
//   `properties`.*,
//   `properties_values_relations`.`relatable_id` as `pivot_relatable_id`,
//   `properties_values_relations`.`property_value_id` as `pivot_property_value_id`
// from
//   `properties`
//   inner join `properties_values` on `properties`.`id` = `properties_values`.`property_id`
//   inner join `properties_values_relations` on `properties_values_relations`.`property_value_id` = `properties_values`.`id`

// where
//   `properties_values_relations`.`relatable_type` = 'Product'
//   and `properties_values_relations`.`relatable_id` in (1);

    // public function propertyValues() : MorphToMany
    // {
    //     return $this->morphToMany(PropertyValue::class, 'relatable', 'properties_values_relations');
    // }

    // public function propertyValues(): BelongsToMany
    // {
    //     return $this->belongsToMany(PropertyValue::class, 'properties_values_relations', 'property_value_id');
    // }

    public function images(): MorphMany
    {
        return $this->MorphMany(Image::class, 'imageable');
    }

    public function pricings(): HasMany
    {
        return $this->hasMany(ProductPrice::class, 'product_id', 'id');
    }

    public function convections(): HasMany
    {
        return $this->hasMany(ProductConvection::class, 'product_id', 'id');
    }

    public function measurements(): HasManyThrough
    {
        return $this->hasManyThrough(ProductConvectionMeasurement::class, ProductConvection::class);
    }

    public function productsSubstratesConvections(): HasMany
    {
        return $this->hasMany(ProductSubstrateConvection::class, 'product_id', 'id');
    }

    // public function addressesUsers(): BelongsToMany
    // {
    //     return $this->belongsToMany(User::class, 'companies_addresses_users', 'company_address_id', 'user_id');
    // }
}
