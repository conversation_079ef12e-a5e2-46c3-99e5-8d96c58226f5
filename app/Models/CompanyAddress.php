<?php

namespace App\Models;

use CommerceGuys\Addressing\Address as AddressingAddress;
use CommerceGuys\Addressing\AddressFormat\AddressFormatRepository;
use CommerceGuys\Addressing\Country\CountryRepository;
use CommerceGuys\Addressing\Formatter\PostalLabelFormatter;
use CommerceGuys\Addressing\Subdivision\SubdivisionRepository;
use Str;
use Symfony\Component\Yaml\Yaml;

class CompanyAddress extends BaseModel
{
    protected $table = 'companies_addresses';

    protected $appends = [
        'label',
    ];

    protected $fillable = [
        'company_id',
        'address_id',
        'locale_id',
        'user_id',
        'type',
        'company',
        'salution',
        'firstname',
        'lastname',
        'email',
        'phone',
        'is_default',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function address()
    {
        return $this->belongsTo(Address::class);
    }

    public function getLabelAttribute()
    {
        $formatter = new PostalLabelFormatter(
            new AddressFormatRepository(),
            new CountryRepository(),
            new SubdivisionRepository(), [
                'locale' => config('locales.' . $this->locale_id . '.id') ?? 'NL',
        ]);

        $AddressLineformat = '{{{road}}} {{{house_number}}}';
        $formats = Yaml::parse(file_get_contents(resource_path('worldwide.yaml')));
        if (count($format = preg_grep('/^.*{{{road}}}.*$/i', explode("\n", $formats[$this->address->country->code ?? 'NL']['address_template'] ?? ''))) == 1) {
            $AddressLineformat = current($format);
        }

        $AddressLineformat = Str::replace('{{{road}}}', $this->address->street ?? '', $AddressLineformat);
        $AddressLineformat = Str::replace('{{{house_number}}}', trim(implode(' ', [
            $this->address->housenumber,
            $this->address->addition,
        ])), $AddressLineformat);

        $address = new AddressingAddress();
        $address = $address
            ->withOrganization($this->company)
            ->withGivenName(trim(implode(' ', [
                $this->salution,
                $this->firstname,
                $this->lastname,
            ])))
            ->withCountryCode($this->address->country->code)
            ->withPostalCode(strtoupper($this->address->postalcode))
            ->withLocality(ucwords($this->address->city))
            ->withAddressLine1(ucwords($AddressLineformat))
        ;

        return $formatter->format($address, [
            'origin_country' => $this->address->country->code,
            'html' => true,
        ]);
    }
}
