<?php

namespace App\Models;

use App\Traits\IsLockable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ItemUnitRelation extends BaseModel
{
    use IsLockable;

    protected $table = 'items_units_relations';

    protected $fillable = [
        'id',
        'item_unit_id',
        'relatable_type',
        'relatable_id',
        'value',
    ];

    public function unit(): BelongsTo
    {
        return $this->belongsTo(
            ItemUnit::class,
            'item_unit_id'
        );
    }

    public function relatable(): MorphTo
    {
        return $this->morphTo();
    }
}
