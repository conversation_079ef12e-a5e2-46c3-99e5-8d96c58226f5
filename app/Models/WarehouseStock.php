<?php

namespace App\Models;

use App\Models\Filters\IsLockedFilter;
use App\Models\Filters\WarehouseFilter;
use App\Models\Filters\WarehouseLocationFilter;
use App\Traits\IsFilterable;
use App\Traits\IsLockable;
use App\Traits\IsSortable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Znck\Eloquent\Traits\BelongsToThrough;

class WarehouseStock extends BaseModel
{
    use BelongsToThrough;
    use IsFilterable;
    use IsLockable;
    use IsSortable;

    protected $table = 'warehouses_stock';

    protected $fillable = [
        'id',
        'uniq_id',
        'warehouse_location_id',
        'item_variant_id',
    ];

    protected $casts = [];

    protected $sortable = [
        'warehouse_location_id',
    ];

    protected $lockable = [];

    protected $lockableRelations = [];

    protected $filters = [
        WarehouseFilter::class,
        WarehouseLocationFilter::class,
        IsLockedFilter::class,
    ];

    public function warehouse(): \Znck\Eloquent\Relations\BelongsToThrough
    {
        return $this->belongsToThrough(
            Warehouse::class,
            WarehouseLocation::class,
            foreignKeyLookup: [
                WarehouseLocation::class => 'warehouse_location_id',
            ]
        );
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(
            WarehouseLocation::class,
            'warehouse_location_id'
        );
    }

    public function itemVariant(): BelongsTo
    {
        return $this->belongsTo(
            ItemVariant::class,
            'item_variant_id'
        );
    }

    public function item(): \Znck\Eloquent\Relations\BelongsToThrough
    {
        return $this->belongsToThrough(
            Item::class,
            ItemVariant::class,
            foreignKeyLookup: [
                ItemVariant::class => 'item_variant_id',
            ]
        );
    }
}
