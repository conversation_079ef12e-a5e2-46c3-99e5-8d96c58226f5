<?php

namespace App\Models;

use App\Enums\DocumentClassification;
use App\Services\DocumentService;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Str;

class DocumentVersion extends BaseModel
{
    protected $table = 'documents_versions';

    protected $fillable = [
        'document_id',
        'name',
        'name_origin',
        'extension',
        'mimetype',
        'size',
        'version',
        'has_image',
        'has_html',
        'has_data',
        'is_processed',
    ];

    protected $casts = [
        'classification' => DocumentClassification::class,
        'version' => 'integer',
        'size' => 'integer',
    ];

    public function siblings(): HasMany
    {
        return $this
            ->hasMany(self::class, 'document_id', 'document_id')
            ->where('document_id', $this->document_id)
        ;
    }

    public function getName(): string
    {
        return $this->name_origin ?? $this->name;
    }

    public function getPath($extension = null): string
    {
        return storage_path(implode('/', [
            'app',
            'private',
            $this->getStoragePath($extension),
        ]));
    }

    public function getStoragePath($extension = null): string
    {
        $path = '/' . implode('/', [
            'documents',
            DocumentService::getGroupDirectory($this->document_id),
            $this->document_id,
            $this->version,
            $this->name,
        ]);

        if (! is_null($extension)) {
            $path = Str::replaceLast($this->extension, $extension, $path);
        }

        return $path;
    }
}
