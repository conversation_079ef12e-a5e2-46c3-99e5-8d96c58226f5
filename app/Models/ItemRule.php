<?php

namespace App\Models;

use App\Enums\ItemRulePricingType;
use App\Enums\ItemRuleType;
use App\Enums\ItemRuleUnitSpecification;
use App\Traits\IsLockable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ItemRule extends BaseModel
{
    use IsLockable;

    protected $table = 'items_rules';

    protected $fillable = [
        'id',
        'item_id',
        'item_variant_id',
        'unit_group_id',
        'type',
        'unit_specification',
        'unit_specification_min',
        'unit_specification_max',
        'pricing_type',
        'pricing_value',
        'is_active',

    ];

    protected $casts = [
        'type' => ItemRuleType::class,
        'unit_specification' => ItemRuleUnitSpecification::class,
        'pricing_type' => ItemRulePricingType::class,
    ];

    public function unitGroup(): BelongsTo
    {
        return $this->belongsTo(
            UnitGroup::class,
            'unit_group_id'
        );
    }

    public function item(): BelongsTo
    {
        return $this->belongsTo(
            Item::class,
            'item_id'
        );
    }

    public function variant(): BelongsTo
    {
        return $this->belongsTo(
            ItemVariant::class,
            'item_variant_id'
        );
    }
}
