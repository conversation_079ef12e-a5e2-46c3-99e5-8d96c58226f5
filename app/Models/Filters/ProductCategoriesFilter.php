<?php

namespace App\Models\Filters;

use Lacodix\LaravelModelFilter\Enums\FilterMode;
use Lacodix\LaravelModelFilter\Filters\BelongsToManyFilter;

class ProductCategoriesFilter extends BelongsToManyFilter
{
    public FilterMode $mode = FilterMode::CONTAINS;

    protected string $field = 'categories';

    protected string $relationModel = \App\Models\Category::class;
    protected string $titleColumn = 'systemname';
    protected string $title = 'Categories';

}
