<?php

namespace App\Models\Filters;

use Illuminate\Database\Eloquent\Builder;
use Lacodix\LaravelModelFilter\Enums\FilterMode;
use Lacodix\LaravelModelFilter\Filters\SelectFilter;

class UnitIsBaseUnitFilter extends SelectFilter
{
    public FilterMode $mode = FilterMode::EQUAL;

    protected string $field = 'unit_is_base_unit_filter';
    protected string $title = 'Is base unit';

    public function optionEmptyTitle(): string
    {
        return 'All units';
    }

    public function options(): array
    {
        return [
            'Base units' => '1',
            'Not base units' => '0',
        ];
    }

    public function apply(Builder $query): Builder
    {
        if ($this->values[$this->field] === '1') {
            return $query->whereHas('isUnitBaseForUnitType');
        }

        return $query->whereDoesntHave('isUnitBaseForUnitType');
    }
}
