<?php

namespace App\Models\Filters;

use App\Enums\ItemType;
use <PERSON>od<PERSON>\LaravelModelFilter\Enums\FilterMode;
use Lacodix\LaravelModelFilter\Filters\SelectFilter;

class ItemTypeFilter extends SelectFilter
{
    protected string $field = 'type';

    protected string $title = 'Types';

    public FilterMode $mode = FilterMode::CONTAINS;

    public function options(): array
    {
        static $cached;

        if ($cached !== null) {
            return $cached;
        }

        // Get currently selected filter value(s)
        $selected = (array) ($this->values[$this->field] ?? []);

        if ($this->model() !== null) {

            $related = $this->model()::query()
                ->select('type')
                ->whereNotNull('type')
                ->distinct()
                ->pluck('type')
                ->map(fn ($enum) => $enum->value)
                ->toArray()
            ;

            $items = array_unique(array_merge($selected, $related));

            $list = collect(ItemType::cases())
                ->filter(function ($case) use ($items) {
                    return in_array(
                        $case->value,
                        $items
                    );
                })
            ;
        }

        return $cached = ($list ?? collect(ItemType::cases()))
            ->mapWithKeys(fn ($case) => [$case->name => $case->value])
            ->toArray();
    }
}
