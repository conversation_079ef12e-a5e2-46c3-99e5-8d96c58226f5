<?php

return [

    'protocol' => 'https',
    'host' => 'api.tin-check.com',
    'path' => 'api.php',

    'content' => 'json',
    'accept' => 'json',

    'authentication' => false,
    'api_key' => '1f37b82134240d8123ef05159d3fe3eb',

    'countrycodes' => [
        "ad", "ae", "ai", "al", "am", "ao", "ar", "at", "au", "aw", "az", "ba", "bb", "bd", "be", "bg",
        "bh", "bn", "bo", "br", "by", "bz", "ca", "ch", "ck", "cl", "cn", "co", "cr", "cu", "cv", "cw",
        "cy", "cz", "de", "dk", "dm", "do", "dz", "ec", "ee", "eg", "es", "et", "eu", "fi", "fj", "fo",
        "fr", "gd", "ge", "gg", "gh", "gi", "gl", "gn", "gr", "gt", "gw", "hk", "hn", "hr", "hu", "id",
        "ie", "il", "im", "in", "ir", "is", "it", "je", "jm", "jp", "ke", "kg", "kn", "kr", "kw", "kz",
        "lb", "lc", "li", "lk", "lt", "lu", "lv", "ma", "mc", "md", "me", "mh", "mk", "mo", "mt", "mu",
        "mv", "mw", "mx", "my", "mz", "ng", "ni", "nl", "no", "nr", "nz", "om", "pa", "pe", "ph", "pk",
        "pl", "pt", "py", "qa", "ro", "rs", "ru", "sa", "sb", "sc", "se", "sg", "si", "sk", "sm", "st",
        "sv", "th", "tn", "tr", "tt", "tw", "ua", "ug", "gb", "us", "uy", "uz", "ve", "vn", "ws", "za",
    ],
];
