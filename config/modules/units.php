<?php

return [
    'controller' => 'units',
    'model' => 'Unit',

    // 'routes' => [
    //     'groups.index|groups:get./{unitType}/groups',
    //     'groups.store|groupsStore:post./{unitType}/groups',
    //     'groups.create|groupsCreate:get./{unitType}/groups/create',
    //     'groups.edit|groupsEdit:get./{unitType}/groups/{unitTypeGroup}/edit',
    //     'groups.destroy|groupsDestroy:get./{unitType}/groups/{unitTypeGroup}/destroy',
    // ]

    'is_filterable' => true,
    'is_sortable' => true,
];
