function setAttributeAddButtonVisibility()
{
    let options = getTemplate('attribute-row')
        .find('select option')
        .map(function() {
            return $(this).val() == '' 
                ? null 
                : $(this).val()
            ;
        })
        .get()
    ;

    let optionsSelected = $('.overview-attributes .row-attribute select option:selected').map(function() {
        return $(this).val();
    }).get();

    let diff = _.difference(
        options, 
        optionsSelected
    );

    $('.btn-attribute-add').set('disabled', diff.length == 0);

    return diff.length == 0;
}

function updateAttributeSelectOptions()
{   
    $('.overview-attributes select').selectpicker('destroy');

    let selected = $('.overview-attributes .row-attribute select option:selected').map(function() {
        return $(this).val() == '' 
            ? null 
            : $(this).val()
        ;
    }).get();

    $('.overview-attributes select option').set('disabled', false);

    $('.overview-attributes select option').not(':selected').each(function() {

        console.log('option', $(this).val());

        if ($(this).val() == '' || $(this).val() == null) {
            return;
        }

        if (selected.indexOf($(this).val()) !== -1) {
            console.log($(this));
            $(this).set('disabled', true);
        }
    });

    $('.overview-attributes select').selectpicker('render');
}

$(function() {

    $(document).on('click', '.modal .btn-attribute-delete-confirm', function() {

        let id = $(this).data('id');

        $('.overview-attributes .row-attribute input.id[value="'+id+'"]').closest('.row-attribute').fadeOut(function() {
            $(this).remove();
            setAttributeAddButtonVisibility();
            updateAttributeSelectOptions();
        });

        removeModal();
    });

    $(document).on('changed.bs.select', '.overview-attributes select', function() {
        updateAttributeSelectOptions();
        setAttributeAddButtonVisibility();
    });

    $('.btn-attribute-add').not(':disabled').on('click', function() {

        let element = getTemplate('attribute-row', {
            id: uniqueId(),
            systemname: '',
            attribute: '',
            is_locked: 0
        });

        element.find('select').selectpicker();

        $('.overview-attributes .overview-body').append(element);

        updateAttributeSelectOptions();
        setAttributeAddButtonVisibility();
    });

    $('select#unit_type_id').on('changed.bs.select', function() {

        let unitType = _data.set('unit_types')
            .where('id', '=', parseInt($(this).val()))
            .getFirst()
        ;

        let action = unitType == null
            ? ['slideUp', 'slideUp', false ]
            : (unitType.mode != 'conversable'
                ? [ 'slideDown', 'slideUp', false ]
                : [ 'slideUp', 'slideDown', true ]
            )
        ;

        $('.panel-compositionals')[action[0]]();
        $('.conversion-to-base')[action[1]]();
        $('.conversion-to-base input').set('required', action[2]);
    });

    if ($('template#attribute-row').length > 0) {
        updateAttributeSelectOptions();
        setAttributeAddButtonVisibility();
    }
});