$(function() {


    $('select#system_context_unit_id').on('changed.bs.select', function () {

        let id = $(this).selectpicker('val'),
            unit, unitType, unitCompositionals, selectElement;

        $('select#system_context_unit_compositional_id').selectpicker('hide');
        $('select#system_context_unit_compositional_unit_id').selectpicker('hide');

        if (id == '') return;

        unit = _data.set('units').where('id', '=', id).getFirst();
        unitType = _data.set('unitTypes').where('id', '=', unit.unit_type_id ?? null).getFirst();
        unitCompositionals = _data.set('unitCompositionals').where('unit_id', '=', id).get() || null;

        if (unitType.mode !== 'compositional') return;

        if (unitCompositionals === null) return;
  
        selectElement = $('select#system_context_unit_compositional_id');
        selectElement.find('option').addClass('d-none');

        unitCompositionals.forEach(function(unitCompositional) {
            selectElement.find('option[value="'+unitCompositional.id+'"]').removeClass('d-none');
        });

        selectElement.find('option.d-none:selected').set('selected', false);

        selectElement
            .selectpicker('destroy')
            .selectpicker('render')
            .selectpicker('show')
        ;
    });

    $(document).on('changed.bs.select', 'select#system_context_unit_compositional_id', function () {

        let id = $(this).selectpicker('val'),
            unitType,
            unitCompositional, 
            selectElement
        ;

        $('select#system_context_unit_compositional_unit_id').selectpicker('hide');

        if (id == '') return;

        unitCompositional = _data.set('unitCompositionals')
            .where('id', '=', id)
            .getFirst()
        ;

        if (unitCompositional === null) return;

        selectElement = $('select#system_context_unit_compositional_unit_id');
        selectElement.find('option').addClass('d-none');

        _data.set('units').get().forEach(function(unit) {

            unitType = _data.set('unitTypes')
                .where('id', '=', unit.unit_type_id ?? null)
                .getFirst()
            ;

            if ((unitType.mode ?? null) !== 'conversable') return;

            selectElement.find('option[value="'+unit.id+'"]').removeClass('d-none');
        });

        selectElement.find('option.d-none:selected').set('selected', false);

        selectElement
            .selectpicker('destroy')
            .selectpicker('render')
            .selectpicker('show')
        ;
    });

    $('select#system_context_unit_id').trigger('changed.bs.select');
    $('select#system_context_unit_compositional_id').trigger('changed.bs.select');
});