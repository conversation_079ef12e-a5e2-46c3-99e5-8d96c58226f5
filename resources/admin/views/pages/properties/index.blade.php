@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li>Properties</li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="Properties">

        <div class="overview">

            <div class="overview-header">

                <div class="row row-item">
                    <div class="col"><x-admin.link.sortable label="System name" attribute="systemname" /></div>
                    <div class="col-2"><x-admin.link.sortable label="System context" attribute="system_context" /></div>
                    <div class="col-2">Context</div>
                    <div class="col-1 text-end"><x-admin.link.sortable label="Values" attribute="values_count" /></div>
                    <div class="col-1 text-end"><x-admin.link.sortable label="Categories" attribute="categories_count" /></div>
                    <div class="col-1 text-center">Global</div>
                    <div class="col-1 text-center">Multiple</div>
                    <div class="col-1 text-center">Filter</div>
                    <div class="col-1 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">
                @foreach($data as $row)
                    
                    <div class="row row-item">
                        <div class="col fw-bold">
                            <a href="{{ route('admin.properties.edit', $row->id) }}">
                                {{$row->systemname}}
                            </a>
                        </div>
                        <div class="col-2">{{ $row->system_context ?? '-' }}</div>
                        <div class="col-2">{{ $row->system_context_systemlabel ?? '-' }}</div>
                        <div class="col-1 text-end">
                            <a href="{{ route('admin.propertiesvalues.index', $row->id) }}">
                                {{$row->values_count}}
                            </a>
                        </div>
                        <div class="col-1 text-end">
                            {{$row->system_context == \App\Enums\SystemContext::MODULE
                                ? ($row->is_global ? '-' : $row->categories_count)
                                : '-'
                            }}
                        </div>
                        <div class="col-1 text-center">
                            {{$row->system_context == \App\Enums\SystemContext::MODULE
                                ? ($row->is_global ? 'Yes' : 'No')
                                : '-'
                            }}
                        </div>
                        <div class="col-1 text-center">
                            {{$row->system_context == \App\Enums\SystemContext::MODULE
                                ? ($row->is_multiple ? 'Yes' : 'No')
                                : '-'
                            }}
                        </div>
                        <div class="col-1 text-center">
                            {{$row->system_context == \App\Enums\SystemContext::MODULE
                                ? ($row->is_filter ? 'Yes' : 'No')
                                : '-'
                            }}
                        </div>
                        <div class="col-1 controls">
                            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemname}}" :disabled="$row->is_locked" />
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no properties yet.</div>
            
        </div>

    </x-admin.panel>

@endsection