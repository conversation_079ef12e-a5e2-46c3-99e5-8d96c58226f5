@extends('admin::layouts.app')

@section('title', empty($data->id) ? 'Create' : 'Edit ' . $data->getFullName())

@section('controls')
    <x-admin.button type="back" />
    <x-admin.button type="save" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.employees.index')}}">{{ucfirst(App::get('module'))}}</a></li>
    <li>{{ empty($data->id) ? 'Add' : $data->getFullName() }}</li>
@endsection

@section('form')
    <x-admin.form 
        :bind="$data"
        class="form-employees" 
        action="{{route('admin.employees.store')}}"
        enctype="multipart/form-data"
        x-data="{ 
            _locale: '{{ config('app.locale') }}'
        }" 
    />
        <x-input type="hidden" name="id" />

@endsection

@section('content')

    <div class="row">
        <div class="col-12 col-lg-5">

            <x-admin.panel title="General information">

                <x-admin.form.row>
                    <x-admin.form.label for="first_name" label="First name" required="true" />
                    <x-admin.form.input name="first_name" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="last_name" label="Last name" required="true" />
                    <x-admin.form.input name="last_name" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="gender" label="Gender" required="true" />
                    <x-admin.form.select :options="\App\Enums\Gender::class" name="gender" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="email" label="Email" required="true" />
                    <x-admin.form.input name="email" required="true" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="phone" label="Phone" />
                    <x-admin.form.input name="phone" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="mobile" label="Mobile" />
                    <x-admin.form.input name="mobile" />
                </x-admin.form.row>

            </x-admin.panel>

            <x-admin.panel title="Passwords">

                <x-admin.form.row>
                    <x-admin.form.label for="password" label="Password" />
                    <x-admin.form.input type="password" name="password" />
                </x-admin.form.row>

                <x-admin.form.row>
                    <x-admin.form.label for="password_confirmation" label="Password confirmation" />
                    <x-admin.form.input type="password" name="password_confirmation" />
                </x-admin.form.row>

            </x-admin.panel>

        </div>
        <div class="col-12 col-lg-7">

            <x-admin.panel title="Profile image">

                <x-admin.form.row>
                    <x-admin.form.label for="profile_image" label="Profile image" />
                    <x-admin.form.col>
                        <x-admin.form.imageuploader 
                            name="profile_image"
                            data-type="single"
                            data-editor="true"
                            data-aspectratio="3x2"
                        />
                    </x-admin.form.col>


                </x-admin.form.row>

            </x-admin.panel>

            <x-admin.panel type="locales" title="Language specific">

                @foreach (config('locales') as $locale => $localeData)
    
                    <x-admin.form.row :locale="$locale">
                        <x-admin.form.label label="Description" for="description" />
                        <x-admin.form.textarea textEditor="plaintext" name="description" />
                    </x-admin.form.row>
        
                @endforeach
        
            </x-admin.panel>

            

        </div>
    </div>
   
@endsection 