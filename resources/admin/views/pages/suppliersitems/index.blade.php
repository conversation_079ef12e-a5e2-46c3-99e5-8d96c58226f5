@extends('admin::layouts.app')

@section('title', $company->exists ? $company->name : 'Suppliers items')

@section('controls')
    @if ($company->exists)
        <x-admin.button button="back" route="{{ route('admin.suppliers.edit', $company->id)}}" />
        <x-admin.button button="create" route="{{ route('admin.suppliersitems.supplier_create', $company->id) }}" />
    @else
        <x-admin.button button="create" route="{{ route('admin.suppliersitems.create') }}" />
    @endif
@endsection

@section('breadcrumbs')
     <li><a href="{{route('admin.suppliers.index')}}">Suppliers</a></li>
     @if ($company->exists)
        <li><a href="{{route('admin.suppliers.edit', $company->id )}}">{{ $company->name }}</a></li>
    @endif
    <li>Items</li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="Items">

        <div class="overview">

            <div class="overview-header">
                <div class="row row-item">
                    <div class="col">
                        <div class="row">
                            <div class="col-5"><x-admin.link.sortable label="Code" attribute="code" module="Items" model="Item" /></div>
                            <div class="col-7"><x-admin.link.sortable label="Systemname" attribute="systemname" model="Item" /></div>
                        </div>
                    </div>
                    <div class="col-2"><x-admin.link.sortable label="Related item" attribute="related.systemname" model="Item" /></div>
                    @if (!$company->exists)
                        <div class="col-2"><x-admin.link.sortable label="Company" attribute="company.name" model="Item" /></div>
                    @endif
                    <div class="col-5">
                        <div class="row gx-1">
                            <div class="col-2 text-truncate"><x-admin.link.sortable label="Type" attribute="type" model="Item" /></div>
                            <div class="col text-truncate"><x-admin.link.sortable label="Warehouse unit" attribute="units.warehouse.groupunit.systemname" model="Item" /></div>
                            <div class="col text-truncate"><x-admin.link.sortable label="Sales unit" attribute="units.sales.groupunit.systemname" model="Item" /></div>
                            <div class="col-2 text-end"><x-admin.link.sortable label="Products" attribute="products_count" model="Item" /></div>
                            <div class="col-2 controls">Actions</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)
                    <div class="row row-item">
                        <div class="col">
                            <div class="row">
                                <div class="col-5 fw-bold text-truncate">
                                    <a href="{{ route('admin.suppliersitems.supplier_edit', [$row->company_id, $row->id]) }}">{{$row->code}}</a>
                                </div>
                                <div class="col-7 text-truncate">
                                    <a href="{{ route('admin.suppliersitems.supplier_edit', [$row->company_id, $row->id]) }}">{{$row->systemname}}</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-2 text-truncate"><a href="{{ route('admin.items.edit', $row?->related?->id) }}">{{$row?->related?->systemlabel}}</a></div>
                        @if (!$company->exists)
                            <div class="col-2 text-truncate"><a href="{{ route('admin.companies.edit', $row?->company_id) }}">{{$row?->company?->name}}</a></div>
                        @endif
                        <div class="col-5">
                            <div class="row gx-1">
                                <div class="col-2 text-truncate">{{ $row->type }}</div>
                                <div class="col text-truncate">{{ $row->units?->firstWhere('unit_group_id', 1)?->unitGroupUnit?->unit?->systemname ?? '-' }}</div>
                                <div class="col text-truncate">{{ $row->units?->firstWhere('unit_group_id', 2)?->unitGroupUnit?->unit?->systemname ?? '-' }}</div>
                                <div class="col-2 text-end">{{ $row->products_count }}</div>
                                <div class="col-2 controls">
                                    <x-admin.button group="overview" button="edit" :parameters="[$row->company_id, $row->id]" />
                                    <x-admin.button 
                                        group="overview" 
                                        button="destroy" 
                                        :route="route('admin.suppliersitems.supplier_destroy', [$row->company_id, $row->id])" data-name="{{$row->systemname}}" :disabled="$row->is_locked" />
                                </div>

                            </div>
                        </div>     
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There are no items yet.</div>
        </div>

    </x-admin.panel>

@endsection