@extends('admin::layouts.app')

@section('title', 'Overview')

@section('controls')
    <x-admin.button button="module" :route="route('admin.warehouses.index')" label="Warehouses" />
    <x-admin.button button="module" :route="route('admin.warehouseslocations.index')" label="Locations" />
    <x-admin.button button="create" />
@endsection

@section('breadcrumbs')
    <li><a href="{{route('admin.warehouses.index')}}">Warehouses</a></li>
    <li>Stock</li>
@endsection

@section('filters', true)

@section('content')

    <x-admin.panel type="overview" title="Warehouses stock">

        <div class="overview">
            <div class="overview-header">
                <div class="row row-item">
                    <div class="col"><x-admin.link.sortable label="Item" attribute="Item" /></div>
                    <div class="col-2"><x-admin.link.sortable label="Warehouse" attribute="warehouse_id" /></div>
                    <div class="col-2"><x-admin.link.sortable label="Location" attribute="warehouse_location_id" /></div>
                    <div class="col-1 text-end"><x-admin.link.sortable label="Quantity" attribute="quantity" /></div>
                    <div class="col-2 controls">Actions</div>
                </div>
            </div>

            <div class="overview-body">
                @foreach ($data as $row)
                    <div class="row row-item">
                        <div class="col fw-bold text-truncate"><a href="{{ route('admin.items.edit', $row?->item?->id) }}">{{ $row?->item?->systemname }}</a></div>
                        <div class="col-2 text-truncate">
                            <a href="{{ route('admin.warehouses.edit', $row->warehouse?->id) }}">{{$row->warehouse?->systemname}}</a>
                        </div>
                        <div class="col-2 text-truncate">
                            <a href="{{ route('admin.warehouseslocations.edit', $row->warehouse_location_id) }}">{{$row->location?->systemname}}</a>
                        </div>
                        <div class="col-1 text-end">{{$row->quantity}}</div>
                        <div class="col-2 controls">
                            <x-admin.button group="overview" button="edit" :parameters="$row->id" />
                            <x-admin.button group="overview" button="destroy" :parameters="$row->id" data-name="{{$row->systemname}}" :disabled="$row->is_locked" />
                        </div>                  
                    </div>
                @endforeach
            </div>

            <div class="overview-empty alert alert-info mt-3">There is no stock yet.</div>
        </div>

    </x-admin.panel>

@endsection