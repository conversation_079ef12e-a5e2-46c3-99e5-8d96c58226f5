
@php($hasEnabled = false)

@foreach($list as $key => $item)

    @if ($key == 'enabled')
        @php($hasEnabled = true)
        <div 
            class="section-list"
            x-data="{ open: {{ $item->value }} }"
        >

            <div class="section-list-switch">

                <x-admin.form.toggle
                    :label="ucfirst($key)"
                    :checked="$item->value"
                    :name="'config_'.$item->id"
                    x-model="open"
                />

            </div>
            <div 
                class="section-list-items" 
                x-show="open === true || open === 1" 
                id="enabled-{{ $item->id }}"
            >

        @continue
    @endif

    @if (is_array($item))
        
        <h3>{{ $key }}</h3>
        
        <x-admin.module.config.collection :list="$item" />
    
        @continue
    @endif

    <x-admin.module.config.item :any="$item" />

@endforeach

@if ($hasEnabled)
        </div>
    </div>
@endif