h1 {

    .badge {
        font-size: 14px;
        margin-left:16px;
        padding: 8px;
    }
}


.badge-draft {
    border: 1px solid #9b9b9b;
    background-color: #fdfdfd;
    color: #7a7a7a;
}
.badge-open {
    border: 1px solid #bb001a;
    background-color: #ff9e9e;
    color: #520505;
}


.badge-waiting_for_sending {
    border: 1px solid #000000;
    background-color: #e2e46d;
    color: #8d0000;
    font-weight: bold;
    border-style: dashed;
}
.badge-supplier_pending {
    border: 1px solid #626900;
    background-color: #e2e46d;
    color: #000000;
    font-weight: bold;
    border-style: dashed;
}

.badge-supplier_rejected {
    border: 1px solid #7e0000;
    background-color: #e2e46d;
    color: #7e0000;
    font-weight: bold;
    border-style: dashed;
}
.badge-supplier_confirmed {
    border: 1px solid #0b8600;
    background-color: #e2e46d;
    color: #085f00;
    font-weight: bold;
    border-style: dashed;
}


.badge-cancelled {
    border: 1px solid #999999;
    background-color: #e7e7e7;
    color: #999999;
}
.badge-40 {
    border: 1px solid #12999e;
    background-color: #76e1e9;
    color: #0d8088;
}
.badge-50 {
    border: 1px solid #6f89af;
    background-color: #dfecff;
    color: #6f89af !important;
}
.badge-60 {
    border: 1px solid #6b6a45;
    background-color: #fcffd0c9;
    color: #45472a !important;
}
.badge-70 {
    border: 1px solid #c59427;
    background-color: #f7e9cb;
    color: #dba42a;
}
.badge-90 {
    border: 1px solid #3b1dc4;
    background-color: #c1b1fa;
    color: #491375;
}
.badge-110 {
    border: 1px solid #ac54c9;
    background-color: #efddf5;
    color: #bd75d5;
}
.badge-130 {
    border: 1px solid #5dc992;
    background-color: #cef5e1;
    color: #5dc992;
}
.badge-150 {
    border: 1px solid #4ea579;
    background-color: #c7e7d7;
    color: #41b57a;
}

.badge-160 {
    border: 1px solid #585858;
    background-color: #d1d1d1;
    color: #2c2c2c;
}