
.label-required::after {
    content: '*';
}

.input-group {
    .input-group-text {
        background: #f9fafd !important;
    }
}

.input-group.input-group-sm {

    .input-group-text {
        height: inherit !important;
        font-size: 0.8rem;
    }

    > .input-group-addon:first-child .bootstrap-select {
        border-radius: 
            var(--falcon-border-radius-sm) 
            0px 
            0px 
            var(--falcon-border-radius-sm) !important;
    }
      
    > .input-group-addon:not(:first-child):not(:last-child) .bootstrap-select {
        border-radius: 0px !important;
    }   

    > .input-group-addon:last-child {
        border-radius: 
            0px 
            var(--falcon-border-radius-sm) 
            var(--falcon-border-radius-sm) 
            0px !important;
    }
}

.form-control,
.bootstrap-select {

    &.disabled {
        opacity: 0.75;
        cursor: not-allowed;

        select {
            cursor: not-allowed;
        }
    }
}


input.form-control {

    font-size: 0.8rem;

    &.disabled,
    &.is-locked {
        opacity: 0.75;
        cursor: not-allowed;
    }
}


.form-control-locked {

    position: relative; /* needed for absolute icon */

    &::before,
    &::after {
        font-family: "Font Awesome 6 Duotone";
        right: 9px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        font-size: 1rem;
        margin-top: 1px;
        position: absolute;
        font-weight: 400;
        opacity: 1 !important;
        color: #ebba00 !important;
    }

    &::before {
        content: "\f023\f023";
        visibility: hidden;
    }

    &::after {
        content: "\f023";      
    }

    &:hover {
        &::before,
        &::after {
            visibility: visible;
        }
    } 
}

.bootstrap-select.form-control {

    line-height: 1.25 !important;
    border: var(--falcon-border-width) solid var(--falcon-gray-300);

    &.disabled,
    &.is-locked {
        > button.dropdown-toggle {
            pointer-events: none !important;
        }
    }

    &.is-locked {

        opacity: 0.75;
        cursor: not-allowed;

        > .dropdown-toggle::after {
            content: unset !important;
            border-top: none !important;
            border-right: none !important;
            border-bottom: none !important;
            border-left: none !important;
        }
    }
}

input[data-type=money],
input[data-type=decimal] {
    text-align: right;
}

.row-group {
    @extend .my-3;
}

.row-block {

    @extend 
        .row,
        .align-items-center;

        &:not(.link-objects) {
            @extend
                .mb-3;
        }
    

    .label {

        margin-bottom: auto !important;

        @extend 
            .col-form-label-sm,
            .col-3;
    }

    .label-5 {

        margin-bottom: auto !important;
        @extend 
            .col-form-label-sm,
            .col-5;
    }

    .input {
        @extend .col;
    }
}


.option-optgroup,
.option-optgroup .text {
    font-weight: bold;
    color: #000 !important; 
    font-size: 1.05em;
}

.value-is-locked {
    display: inline-block;
    padding-top: calc(0.3125rem + var(--falcon-border-width));
    padding-bottom: calc(0.3125rem + var(--falcon-border-width));
    margin-bottom: 0;
    font-size: inherit;
    font-weight: 500;
    line-height: 1.5;
}