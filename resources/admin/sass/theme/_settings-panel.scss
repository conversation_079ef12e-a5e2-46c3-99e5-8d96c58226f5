
/* -------------------------------------------------------------------------- */
/*                               Settings Panel                               */
/* -------------------------------------------------------------------------- */

.settings-panel{
  max-width: 22rem;
}

.settings-panel-header {
  align-items: flex-start;
  padding: 0.5rem 1.25rem;
  &:before,
  &:after {
    border-radius: 50%;
    height: 12.5rem;
    width: 12.5rem;
  }
  &:before {
    left: 5.125rem;
    top: 1.188rem;
  }
  &:after {
    right: -6.25rem;
    top: -7.938rem;
  }
}

.btn-group-navbar-style{
  .btn-check{
    + .btn-navbar-style{
      box-shadow: none;
      padding: 0;
      margin-bottom: 1rem;
      text-align: left;
      .img-prototype{
        border: 3px solid var(--#{$prefix}gray-200);
        border-radius: $border-radius-lg;
        transition: border 0.2s ease;
        margin-bottom: 0.5rem;
        background-color: var(--#{$prefix}gray-100);
      }
      .label-text{
        position: relative;
        padding-left: 1.5rem;
        &:after{
          position: absolute;
          content: '';
          left: 0;
          width: 1rem;
          height: 1rem;
          border: 1px solid var(--#{$prefix}gray-500);
          border-radius: 50%;
          top: 50%;
          transform: translateY(-50%);
          transition: border 0.2s ease;
        }
      }
    }
    &:checked:not([disabled]) + .btn-navbar-style{
      .img-prototype{
        border-color: var(--#{$prefix}success);
      }
      .label-text::after{
        border: 5px solid var(--#{$prefix}success);
      }
    }
    &:disabled{
      + .btn-navbar-style{
        opacity: 0.5;
      }
    }
  }
  .hover-overlay {
    position: relative;
    overflow: hidden;
    &:after {
      position: absolute;
      content: "";
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: transparent;
      z-index: 1;
      transition: $transition-base;
    }
    @include hover-focus {
        &:after {
          background: rgba($black, 0.2);
        }
    }
  }
}

.setting-toggle {
  position: fixed;
  top: 50%;
  right: 0;
  text-decoration: none;
  z-index: 1016;
  transform: rotate(-90deg) translate3d(-25px, 39px, 0);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  margin-top: -83px;
  box-shadow: var(--#{$prefix}setting-toggle-shadow);

  @include media-breakpoint-down(sm) {
    transform: rotate(-90deg);
    outline: none;
    .bg-primary-subtle {
      background-color: transparent !important;
    }
    small {
      display: none;
    }
  }

  &:hover{
    text-decoration: none !important;
  }
}