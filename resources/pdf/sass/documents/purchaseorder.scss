
body.purchaseorder {

    div.page {
        size: 21cm 29.65cm;

        display: flex;
        flex-flow: column;

        margin:0px;
        padding-top:15mm;  
        padding-left:15mm;
        padding-right:15mm; 
        padding-bottom:12mm;
        height: 100%;
        width: 100%;   
        width:1000px;
        height: 1412px;
        position: relative;  
        background: white;

        @media not print {
            float: left;
            margin-left: 50px; 
            margin-top:15px;
            margin-bottom:15px;
            page-break-before: none;
            page-break-inside: none;
            zoom:0.9;
        }

        .overview {

            .overview-header,
            .overview-body .row-item,
            .totals .row {

                > div:first-child {
                    padding-left: 0px !important;
                }

                > div:last-child {
                    padding-right: 0px !important;
                }
            }

            .overview-header {
                border-bottom: 1px solid #ddd;
            }

            .overview-body {
                .row-item:not(:last-child) {
                    border-bottom: 1px solid #ddd;
                }
            }
        }
    }
}