@php   
use App\Models\User;

$companyChange = '';

// <div class="d-block d-lg-none px-3">
// </div>
// This for the padding in mobile hubloader button TODO move it from here

$hubLoader ='
    <button class="btn btn-hubloader btn-primary" data-url="https://testingportal.vanstraaten.com/en/hubloader">
        <i class="icon fas fa-upload"></i> 
        <span class="title-text">HubLoader</span>
        <div class="more-info"> 
            <i class="fas fa-info"></i> 
            <div class="more-info-box"> 
                <h3>HubLoader</h3> 

                <p>Stop wasting your time waiting for print files to be uploaded!
                    <br><br>The HubLoader opens a new screen where you can start uploading all your print files before configuring your products.
                    <br>As soon as you have finished compiling your order, your print files are ready for use.
                </p> 

                <div class="alert alert-info" role="alert">
                    <b>TIP:</b> When the file name of the print file is equal to the reference of the product,
                    it will be linked automatically.
                </div>
                
            </div> 
        </div> 
    </button>
';

if (User::isLoggedIn()) {

    $changeIcon = '';

    if(session('user')->companies->count() > 1 ) {
        $changeIcon = '
            <a href="'.URL::temporarySignedRoute('login.companies.change', config('global.authentication.available_time'), session('user')).'" class="icon d-block">
                <i class="fas fa-exchange-alt"></i>
            </a>
        ';
    }

    $companyChange = 
        '<div class="company-change justify-content-between mt-auto p-2 align-items-start rounded">
            '.session('company')->name.
            $changeIcon.
        '</div>';
}

$snippets = [];

if (!$agent->isMobile()) {
    $snippets = [ 
        $companyChange,
        $hubLoader,
        '<div class="toggle"> 
            <i class="fas fa-chevron-right"></i>
        </div>',
     ];
}

$data = [
    'login' => [
        'icon' => 'fas fa-home',
        'title' => 'Dashboard',
        'url' => '',

    ],
    'products' => [
        'icon' => 'fa-solid fa-sitemap',
        'title' => 'Producten',
        'url' => 'producten',
    ],
    'categories' => [
        'icon' => 'fa-solid fa-sitemap',
        'title' => 'Categorieen',
        'is_sidemenu' => true,
        'snippets' => $snippets,
        'childs' => [
            [
                'title' => 'Configurators',
                'text' => 'Easily assemble your product through one of our configurators.',
                'class' => 'header-items-configurators',
                'childs' => [
                   [
                        'title' => 'Silicone edge graphic Configurator',
                        'img' =>  URL::asset('/img/silicon-edge.jpg'),
                        'is_product' => true,
                        'is_configurator' => true
                    ],
                    [
                        'title' => 'Sticker configurator',
                        'img' =>  URL::asset('/img/sticker-configurator.jpg'),
                        'is_product' => true,
                        'is_configurator' => true
                    ], 
                   [
                        'title' => 'Silicone edge graphic Configurator',
                        'img' =>  URL::asset('/img/silicon-edge.jpg'),
                        'is_product' => true,
                        'is_configurator' => true
                    ],
                    [
                        'title' => 'Sticker configurator',
                        'img' =>  URL::asset('/img/sticker-configurator.jpg'),
                        'is_product' => true,
                        'is_configurator' => true
                    ], 
                   [
                        'title' => 'Silicone edge graphic Configurator',
                        'img' =>  URL::asset('/img/silicon-edge.jpg'),
                        'is_product' => true,
                        'is_configurator' => true
                    ],
                    [
                        'title' => 'Sticker configurator',
                        'img' =>  URL::asset('/img/sticker-configurator.jpg'),
                        'is_product' => true,
                        'is_configurator' => true
                    ], 
                   [
                        'title' => 'Silicone edge graphic Configurator',
                        'img' =>  URL::asset('/img/silicon-edge.jpg'),
                        'is_product' => true,
                        'is_configurator' => true
                    ]
                ],
            ], [
                'title' => 'Fabrics',
                'text' => 'Create one of our fabrics entirely according to your wishes via our configurator',
                'rows' => 3,
                'childs' => [
                    [
                        'title' => 'Sublimation',
                        'childs' => [
                            [
                                'title' =>'320CM',
                                'childs' => [
                                    [
                                        'title' => 'Backlit SEG 310',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'BlockOut Textile BlackBack Stretch 320',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'DecoCirculair',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'DecoTex 320',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'Eco Mesh 320',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'Flag 320',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'MuroSubli DS',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'SuperFlag 310',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'Viroblock Textile',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'VoileTex',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'BlockOut Textile BlackBack 320',
                                        'is_product' => true,
                                    ],
                                ],
                            ],
                            [
                                'title' =>'500CM',
                                'childs' => [
                                    [
                                        'title' => 'BlockOut Textile BlackBack 500',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'BlockOut Textile BlackBack Stretch 500',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'DecoTex 500',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'Eco Mesh 505 - DS 500 cm - 250 grs',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'Flag 500',
                                        'is_product' => true,
                                    ],
                                    [
                                        'title' => 'WallTex Opaque Easyfix',
                                        'is_product' => true,
                                    ],
                                ],
                            ],

                        ]
                    ], [
                        'title' => 'UV',
                        'childs' => [
                            [
                                'title' => 'Blockout Textile BlackBack 320',
                                'is_product' => true,
                            ],
                            [
                                'title' => 'Blockout Textile Whiteback 310',
                                'is_product' => true,
                            ],
                            [
                                'title' => 'BlueMagic 320',
                                'is_product' => true,
                            ],
                            [
                                'title' => 'Floor Vinyl 315',
                                'is_product' => true,
                            ],
                            [
                                'title' => 'Mesh B1 320 - - 370 grs',
                                'is_product' => true,
                            ],
                            [
                                'title' => 'Pearl 320',
                                'is_product' => true,
                            ],
                        ],
                    ], [
                        'title' => 'Sustainable',
                        'childs' => [
                            [
                                'title' => 'DecoCirculair',
                                'is_product' => true,
                            ], [
                                'title' => 'Eco Mesh 320',
                                'is_product' => true,
                            ], [
                                'title' => 'MuroSubli DS',
                                'is_product' => true,
                            ], [
                                'title' => 'MuroSubli UVTex',
                                'is_product' => true,
                            ],[
                                'title' => 'Eco Wallpaper UV 500',
                                'is_product' => true,
                            ],
                        ],
                    ],
                ],
            ], [
                'title' => 'Stickers',
                'text' => 'Customize your sticker with the desired appearance.',
                'rows' => 2,
                'childs' => [
                    [
                        'title' => 'product a',
                                'is_product' => true,
                    ],
                ],
            ], [
                'title' => 'Panels',
                'text' => 'Easily create a custom panel through our configurator',
                'rows' => 2,
                'childs' => [
                    [
                        'title' => 'product a',
                                'is_product' => true,
                    ],
                ],
            ], [
                'title' => 'SEG',
                'text' => 'Available custom-made for illuminated, non-illuminated, and light-blocking applications.',
                'rows' => 2,
                'childs' => [
                    [
                        'title' => 'product a',
                        'is_product' => true,
                    ],
                ],
            ], [
                'title' => 'BeMatrix',
                'text' => 'Textile cloths and panels specially designed for BeMatrix frames.',
                'rows' => 1,
                'childs' => [
                    [
                        'title' => 'product a',
                        'is_product' => true,
                    ],
                ],
            ], [
                'title' => 'AluVision',
                'text' => 'Textile cloths and panels specially designed for AluVision frames.',
                'rows' => 1,
                'childs' => [
                    [
                        'title' => 'product a',
                        'is_product' => true,
                    ],
                ],
            ],
        ],
    ],
    'offertes' => [
        'icon' => 'fas fa-tachometer-alt',
        'title' => 'Offertes',
    ],
    'orders' => [
        'icon' => 'fas fa-file-alt',
        'title' => 'Orders',
    ],
    'sjablonen' => [
        'icon' => 'fas fa-receipt',
        'title' => 'Sjablonen',
    ],
    'importer' => [
        'icon' => 'fas fa-file-import',
        'title' => 'Importer',
    ],
    'account' => [
        'icon' => 'fas fa-user',
        'title' => 'Instellingen',
        'childs' => [
            [
                'title' => 'Accountinstellingen',
                'url' => 'account',
                'icon' => "fa-solid fa-user"
            ],
            [
                'title' => 'Bedrijfsgegevens',
                'url' => 'account/bedrijf',
                'icon' => "fa-regular fa-building"
            ],
            [
                'title' => 'Adresboek',
                'url' => 'account/adres',
                'icon' => "fa-solid fa-address-book"
            ],
            [
                'title' => 'Contactpersonen',
                'url' => 'account/contactpersonen',
                'icon' => "fa-solid fa-users"
            ],
            [
                'title' => 'Uitloggen',
                'url' => 'uitloggen',
                'icon' => 'fa-solid fa-right-from-bracket'
            ]
        ]
    ],
];


$currentPath = request()->path();

          
@endphp

<header class="container-fluid">

    <div class="header-container row">

        <div class="col-2 order-1 logo-block">
            <a href="/">
                <img src="{{ URL::asset('/img/logo.png') }}" class="logo" alt="logo">
            </a>
        </div>
        
        <div class="col order-2 header-menu">

            @loggedIn
            
                <div class="menu-items">

                    <div class="menu-mobile">
                        
                        <div class="top">

                            <div class="header d-flex d-lg-none">

                                <div class="icon-box d-center">
                                </div>

                                <div class="title me-auto">
                                    Menu
                                </div>

                                <div class="close">
                                    <i class="fa-solid fa-xmark"></i>
                                </div>
                            </div>


                                <div class="body">
                                    <ul class="nav">
                    
                                        {{-- @foreach ($data as $item)
                                            {!! Temp::getItem($item , 0) !!}
                                        @endforeach --}}
                    
                                    </ul>

                                    @if ($agent->isMobile())

                                        {!! $companyChange !!}

                                        <div class="mobile-hubloader-container">
                                            {!! $hubLoader !!}
                                        </div>
                                    @endif

                                </div>
                        </div>

                        <div class="footer">
                            {{-- <div class="p-3  mb-auto d-flex d-lg-none">

                                <a href="/winkelwagen" class="header-icon-item d-inline-flex">
                                    <i class="fas fa-shopping-cart"></i>
                                </a>

                                <div class="ps-2 d-inline-flex justify-content-center flex-column">
                                    <div class="fw-bold">0 items</div>
                                </div>
                            </div> --}}

                            <div class=" w-100 p-3 pt-0  d-lg-none">
                                <a href="/contact" class="d-block d-lg-none btn btn-primary"> Neem contact op</a>
                            </div>
                        </div>
                    </div>

                </div>
            @endloggedIn
        </div>
        
        <div class="{{ session('company_id') ? 'col-auto' : 'col-1'}} languages pe-md-0 order-3">
            <img src="{{ URL::asset('/img/flags/4x3/' .App::getLocale() . '.svg') }}" class="rounded flag current-language" alt="Flag">
            <div class="overlay"></div>
            <div class="languages-list">
                <ul class="list-unstyled m-0 p-0">
                    @foreach(config('languages') as $code => $language)
                    
                        @if ($code !== App::getLocale())
                            <li class="">
                                <a href="{{ url($code).'/'.request()->segment(2) }}">
                                    <img src="{{ asset('/img/flags/4x3/' . $code . '.svg') }}" class="flag" alt="Flag">
                                    <span class="d-inline-block ml-3 font-md text-capitalize">{{ $language['name'] }}</span>
                                </a>
                            </li>
                        @endif
                    @endforeach
                </ul>
            </div>
        </div>
    
        

        @loggedIn
        
            <div class="col-12 order-5 order-md-4 col-md-auto pe-0 search-block">
                <ul class="nav header-icons">
                    <li class="header-item">

                        <div class="header-icon-item search">

                            <div class="search-input">
                                <input type="text" class="form-control" placeholder="Waar ben je naar op zoek?">
                            </div>

                            <div class="search-content p-3 d-none">
                                <div class="content">

                                    <a href="/producten/specifiek" class="d-block mb-3">
                                        
                                        <div class="row mx-0">

                                            <div class="col-auto search-image-container p-0">
                                                <div class="image">
                                                    <div class="ratio ratio-3x2 ratio-cover">
                                                        <img src="https://testingportal.vanstraaten.com//media/products/39/blockout-textile-blackback-320-uv-500-cm-260-grs-8bef0ff7-1acc-4d23-9fa0-1dc09ad2bb60_thumb.jpg">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col align-self-center search-titles text-truncate ">
                                                <div class="header-dropdown-title pb-1">
                                                    Blockout Textile BlackBack 320 - UV 500 cm - 260 grs
                                                </div>
                                            </div>
                                        </div>

                                    </a>

                                    <a href="/producten/specifiek" class="d-block mb-3">
                                        
                                        <div class="row mx-0">

                                            <div class="col-auto search-image-container p-0">
                                                <div class="image">
                                                    <div class="ratio ratio-3x2 ratio-cover">
                                                        <img src="https://testingportal.vanstraaten.com//media/products/39/blockout-textile-blackback-320-uv-500-cm-260-grs-8bef0ff7-1acc-4d23-9fa0-1dc09ad2bb60_thumb.jpg">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col align-self-center search-titles text-truncate ">
                                                <div class="header-dropdown-title pb-1">
                                                    Blockout Textile BlackBack 320 - UV 500 cm - 260 grs
                                                </div>
                                            </div>
                                        </div>

                                    </a>

                                    <a href="/producten/specifiek" class="d-block mb-3">
                                        
                                        <div class="row mx-0">

                                            <div class="col-auto search-image-container p-0">
                                                <div class="image">
                                                    <div class="ratio ratio-3x2 ratio-cover">
                                                        <img src="https://testingportal.vanstraaten.com//media/products/39/blockout-textile-blackback-320-uv-500-cm-260-grs-8bef0ff7-1acc-4d23-9fa0-1dc09ad2bb60_thumb.jpg">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col align-self-center search-titles text-truncate ">
                                                <div class="header-dropdown-title pb-1">
                                                    Blockout Textile BlackBack 320 - UV 500 cm - 260 grs
                                                </div>
                                            </div>
                                        </div>

                                    </a>

                                    <a href="/producten/specifiek" class="d-block mb-3">
                                        
                                        <div class="row mx-0">

                                            <div class="col-auto search-image-container p-0">
                                                <div class="image">
                                                    <div class="ratio ratio-3x2 ratio-cover">
                                                        <img src="https://testingportal.vanstraaten.com//media/products/39/blockout-textile-blackback-320-uv-500-cm-260-grs-8bef0ff7-1acc-4d23-9fa0-1dc09ad2bb60_thumb.jpg">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col align-self-center search-titles text-truncate ">
                                                <div class="header-dropdown-title pb-1">
                                                    Blockout Textile BlackBack 320 - UV 500 cm - 260 grs
                                                </div>
                                            </div>
                                        </div>

                                    </a>

                                    <a href="/producten/specifiek" class="d-block mb-3">
                                        
                                        <div class="row mx-0">

                                            <div class="col-auto search-image-container p-0">
                                                <div class="image">
                                                    <div class="ratio ratio-3x2 ratio-cover">
                                                        <img src="https://testingportal.vanstraaten.com//media/products/39/blockout-textile-blackback-320-uv-500-cm-260-grs-8bef0ff7-1acc-4d23-9fa0-1dc09ad2bb60_thumb.jpg">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col align-self-center search-titles text-truncate ">
                                                <div class="header-dropdown-title pb-1">
                                                    Blockout Textile BlackBack 320 - UV 500 cm - 260 grs
                                                </div>
                                            </div>
                                        </div>

                                    </a>
                                    
                                </div>
                                <div id="search-loader" class="search-loader"><div class="loading-search"><i class="fa fa-cog fa-spin fa-2x fa-fw"></i></div></div>
                                <a href="#" class="btn btn-primary w-100 btn-sm btn-show-results mt-3 d-center">Laat resultaten zien</a>
                            </div>

                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </li>
                </ul>
            </div>

            <div class="col-auto order-4 order-md-5 text-end ps-0 controls">

                <ul class="nav header-icons">
                    <li class="header-item">
                        <a href="/winkelwagen" class="header-icon-item">
                            <i class="fas fa-shopping-cart"></i>
                        </a>
                    </li>
                    <li class="header-item account-information d-none d-lg-flex">
                        
                        <a href="/account" class="header-icon-item">
                            <i class="fas fa-user"></i>
                        </a>
                        
                        <div class="overlay"></div>
                        
                        <div class="account-list pt-0 d-flex flex-column">
                            {{-- <a href="{{route('account.index')}}" class="item mb-1 justify-content-between">
                                Account
                                
                                <div class="icon-box d-center me-2">
                                    <i class="fa-solid fa-user"></i>
                                </div>
                            </a>

                            <a href="{{route('account.company')}}" class="item mb-1 justify-content-between align-items-center">
                                Bedrijfsgegevens

                                <div class="icon-box d-center me-2">
                                    <i class="fa-regular fa-building"></i>
                                </div>
                            </a>

                            <a href="{{route('account.contactpersons')}}" class="mb-1 item justify-content-between align-items-center">
                                Contactpersonen

                                <div class="icon-box d-center me-2">
                                    <i class="fa-solid fa-users"></i>
                                </div>
                            </a>

                            <a href="{{route('account.addressbook')}}" class="item mb-1 justify-content-between align-items-center">
                                Adresboek

                                <div class="icon-box d-center me-2">
                                    <i class="fa-solid fa-address-book"></i>
                                </div>
                            </a> --}}

                            <a href="{{route('web.auth.logout')}}" class="item mb-1 mt-2 justify-content-between align-items-center">
                                
                                Uitloggen

                                <div class="icon-box d-center me-2">
                                    <i class="fa-solid fa-right-from-bracket"></i>
                                </div>
                            </a>
                        </div>
                    </li>
                    <li class="header-item d-lg-none ">
                        <div class="header-icon-item open-mobile-menu">
                            <i class="fas fa-bars"></i>
                        </div>
                    </li>
                </ul>

            </div>
        @endloggedIn

    </div>
</header>