@extends($app.'.layouts.app')
@section('content')
<div class="row">
    <div class="page-top col-12">
        <x-portal.breadcrumb page="Orders"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <h1 class="align-items-center d-flex">
                {{$order->reference}} 
                <span class="fw-normal ps-1">(#{{$order->order_number}})</span>
                <div class="badge badge-status fw-bold ms-2">{{ $order->status }}</div>
            </h1>
        </div>
    </div>
    
    <div class="page-content col-12 col-lg-8">

        <div class="card card-panel">
            
            <div class="card-header d-none d-md-block">
                <div class="row align-items-center">
                    <div class="col-2 title text-truncate">Product</div>
                    
                    <div class="col text-end title text-truncate">
                        <div class="row justify-content-end">
                            <div class="col-3 text-end">Total amount</div>
                            <div class="col-1"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-body p-0 ">
                @include($app.'.templates.cart_item' , [
                    'data' => $order
                ])
            </div>
        </div>
    </div>

    <aside class="page-aside col-12 col-lg-4">
        <div class="row">
            <div class="col-12 col-md-6 col-lg-12">
                <div class="card card-panel">
                    <div class="card-header">
                        <div class="title">
                            <h2>Actions</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="btn btn-tertiary d-block">
                            Modify
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-md-6 col-lg-12">
                <div class="card card-panel">
                    <div class="card-header">
                        <div class="title">
                            <h2>Order information</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row pb-2">
                            <div class="col-auto title">Order number:</div>
                            <div class="col text-end text">{{$order->id}}</div>
                        </div>
                        <div class="row pb-2">
                            <div class="col-auto title">Your reference:</div>
                            <div class="col text-end text">{{$order->reference}}</div>
                        </div>
                        <div class="row pb-2">
                            <div class="col-auto title">Shipping method:</div>
                            <div class="col text-end text">UPS - Standard</div>
                        </div>
                        <div class="row pb-2">
                            <div class="col-auto title">Contact:</div>
                            <div class="col text-end text">Harrald Vissers</div>
                        </div>
                        <div class="row pb-2">
                            <div class="col-auto title">Sales manager:</div>
                            <div class="col text-end text"><a class="link" href="">Marc Hagens</a></div>
                        </div>
                        <div class="row pb-2">
                            <div class="col-auto title">Quotation date:</div>
                            <div class="col text-end text"> 
                                {{ \Carbon\Carbon::parse($order->confirmation_sent_at)->format('j F Y') }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-auto title">Delivery date:</div>
                            <div class="col text-end text">
                                {{ \Carbon\Carbon::parse($order->delivery_at)->format('j F Y') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        @foreach ($order->addresses as $address)
        
            @php
                        
                $salutation = '';

                if (isset($address->pivot->gender)) {
                    $salutation = $address->pivot->firstname == 'Financiële' 
                        ? 't.a.v.'
                        : ($address->pivot->gender == 'male' 
                            ? 'Dhr.' 
                            : 'Mevr.'
                    );
                }

            @endphp
                
            <div class="col-12 col-md-6 col-lg-12">
                <div class="card card-panel">
                    <div class="card-header">
                        <div class="title">
                            <h2>{{$address->pivot->type}}</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="text">{{$address->pivot->company}}</div>
                        <div class="text">{{ $salutation }} {{$address->pivot->firstname}}{{' '.$address->pivot->lastname ?? ' '}}</div>
                        <div class="text">{{$address->getAddress()}}</div>
                        <div class="text">{{$address->getPostalcodeAndStreet()}}</div>
                        <div class="text">{{$address->country->{'name_'.App::getLocale()} }}</div>
                    </div>
                </div>
            </div>

        @endforeach

            <div class="col-12 col-md-6 col-lg-12">
                <div class="card card-panel">
                    <div class="card-header">
                        <div class="title">
                            <h2>FILES</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <a class="btn btn-primary w-100 text-start">
                            <i class="fa-solid fa-file-pdf ps-2 me-4"></i>
                            Order confirmation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    
    
    </aside>
</div>

@endsection