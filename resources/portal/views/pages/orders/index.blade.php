@extends($app.'.layouts.app')
@section('content')
<div class="row data-container">
    
    <div class="page-top col-12">
        <x-portal.breadcrumb page="Orders"/>
    </div>
    
    <div class="page-heading col-12">
        <div class="page-title">
            <h1>Orders</h1>
        </div>
    </div>
    
    @if ($data->count() > 0)
        <div class="page-content col-12">
            <div class="row justify-content-end pb-4">
                <div class="col-12 col-lg-auto">
                    <div class="form-icon-container ">
                        <input 
                            type="text" 
                            class="form-control font-md" 
                            name="dashboard-orders-search" 
                            id="dashboard-orders-search" 
                            placeholder="Search by reference or Order number">
                        <label for="dashboard-orders-search" class="d-flex justify-content-center align-items-center">
                            <i class="fas fa-search"></i>
                        </label>
                    </div>
                </div>

                <div class="col-12 pt-3 pt-lg-0 col-lg-auto">
                    <div class="text-end align-self-end align-self-lg-center order-4 order-lg-3">
                        <div class="container">
                            <div class="row mb-0">
                                <label class="col-auto align-self-center title fw-bold font-md" for="result-sorting">Sorting:</label>
            
                                <select class="col form-select result-sorting font-md" id="result-sorting">
                                    <option value="order_number desc" {{implode(' ' , $sort) == 'order_number desc' ? 'selected' : ''}}>Order number Descending</option>
                                    <option value="order_number asc" {{implode(' ' , $sort) == 'order_number asc' ? 'selected' : ''}}>Order number Ascending</option>
                                    <option value="delivery_at desc" {{implode(' ' , $sort) == 'delivery_at desc' ? 'selected' : ''}}>Delivery date Descending</option>
                                    <option value="delivery_at asc" {{implode(' ' , $sort) == 'delivery_at asc' ? 'selected' : ''}}>Delivery date Ascending</option>
                                    <option value="total_incl desc" {{implode(' ' , $sort) == 'total_incl desc' ? 'selected' : ''}}>Total amount Descending</option>
                                    <option value="total_incl asc" {{implode(' ' , $sort) == 'total_incl asc' ? 'selected' : ''}}>Total amount Ascending</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                <div class="card card-panel overview">
                    
                    <div class="card-header">
                        <div class="row">
                            <div class="col-12 col-lg-1 title text-truncate d-none d-lg-block">Order number</div>
                            <div class="col-6 col-lg   title text-truncate">Reference</div>
                            <div class="col-6 col-lg-2 title text-truncate text-end">Total amount</div>
                            <div class="col-12 col-lg-2 title text-truncate text-end d-none d-lg-block">Order date</div>
                            <div class="col-12 col-lg-2 title text-truncate text-end d-none d-lg-block">Delivery date</div>
                            <div class="col-12 col-lg-2 title text-truncate text-end d-none d-lg-block">Status</div>

                        </div>
                    </div>
                    <div class="card-body p-0 ">
                        @foreach ($data as $row)

                            <a href="{{route('orders.specific' , $row)}}" class="row text-decoration-none item mx-0 px-1 py-3 align-items-center">

                                <div class="col-2 col-lg-1 text order-0 text-decoration-underline d-none d-lg-block">{{ $row->order_number }}</div>

                                <div class="col-7 col-lg title order-1 text-decoration-underline">{{ $row->reference }}</div>

                                <div class="col-5 col-lg-2 text  order-2 text-end">{{Number::currency($row->total_incl, in: 'EUR', locale: App::getLocale())}}</div>

                                <div class="col-12 col-lg-2 text order-3 order-lg-2 text-end d-none d-lg-block">{{ date('d-m-Y', strtotime($row->created_at))}}</div>

                                <div class="col-12 col-lg-2 text order-4 order-lg-3 text-end d-none d-lg-block">{{ date('d-m-Y', strtotime($row->delivery_at))}}</div>

                                <div class="col-3 col-lg-2  text order-2 order-lg-4 text-end d-none d-lg-block">
                                    <div class="badge badge-status fw-bold">Bestanden geüpload</div>
                                </div>
                            </a>
                        @endforeach                
                    </div>
                </div>
        </div>

        <div class="col-12">

            <div class="row">
                
                <x-portal.limiter :any="$data" />
                
                <x-portal.pagination :maxItems="10" :any="$data" />
            </div>
        </div>
    @else
        <div class="col-12">
            <div class="alert alert-info">
                Momenteel zijn er geen orders.
            </div>

        </div>
    @endif

</div>

@endsection