.d-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.d-horizontal {
    display: flex;
    justify-content: center;
}


.overlay-gradient,
.read-more-gradient {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    z-index: 60;
    background-color: rgba(0,0,0,0.7);
	cursor: pointer;
}

.ratio {

    &.ratio-cover {

		img, .image {
			object-fit: cover;
		}
	}
    
	&.ratio-contain {
		
		img, .image {
			object-fit: contain;
		}
	}

}

.fw-bold {
	font-weight: 600;
}

.fw-bolder {
	font-weight: 700;
}

.fw-normal {
	font-weight: 400;
}

.font-sm {
    font-size: $font-size-sm;
}

.font-md {
    font-size: $font-size-md;
}

.font-lg {
    font-size: $font-size-lg;
}

.text-color {
    color: $text-color !important;
}

.color-green {
	color:#41b57a;
}

.cursor-pointer {
	cursor: pointer;
}

.title-color {
    color: $title-color !important;
}

input:read-only {
	background-color: #e9ecef;

	&:focus {
		background-color: #e9ecef;
	}
}

.label-required::after {
	content: '*';
}

.row-cols-234444 {
	@extend
		.row,
		.row-cols-2,
		.row-cols-sm-3,
		.row-cols-md-4
}

.row-cols-2344445 {
	@extend
		.row,
		.row-cols-2,
		.row-cols-sm-3,
		.row-cols-md-4,
		.row-cols-xxl-5
}

.badge-status {
	background-color: #fff;
	font-size: $font-size-sm;
	color: $text-color;
	border: 1px solid $text-color;
	padding: 0.5rem;
}

.alert-warning {
	color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
	font-size: 15px;
	text-align: center;
}

// Makes images centered in a fixed by aspect ratio div
.rratio {
	width: 100%;
	padding-top: 100%;
	position: relative;
	overflow: hidden;
	
	&.rratio-cover {
		.inner {
	
			img {
				object-fit: cover;
			}
		}
	}
	&.rratio-contain {
		.inner {
	
			img {
				object-fit: contain;
			}
		}
	}
	
	&.rratio-3x2 {
		padding-top: 66.66%;
	}

	.inner {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;

		img {
			width: 100%;
			height: 100%;
		}
	}
}

.delivery-type {
    display: flex;
    align-items: center;
}

.row-warning {
    background: #f3a8a854 !important;
    margin-left: 0px;
    margin-right: 0px;
}

i.icon-check {
    display: none;
    position: absolute;
    top: 7px;
    right: 10px;
    width: 18px;
    height: 18px;
    font-size: 18px;
    color: #41b57a;
}

.delivery-type-many {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center;
    padding: .5rem !important;
    padding-top: 20px !important;
    padding-bottom: 16px !important
}

i.icon-type {

    color: #41b57a;
    font-size: 25px;
    text-decoration: unset !important;
}

.delivery-type,
.delivery-date,
.delivery-time {

    position: relative;
    cursor: pointer;
    text-decoration: none !important;

    p {
        text-decoration: none;
        padding:0px !important;
        margin:0px !important;
    }

    .info {

        display: none;
        position: absolute;
        top: 7px !important;
        right: 10px !important;
        font-size: 16px;
        color: #41b57a;

        div {
            display: none;
            z-index:1070;
            width: 250px;
            left: 20px;
            top: 0px;
            font-family: "Open Sans";
            color: initial;
            line-height: 1.3em; 
        }

        &:hover {
            div {
                display: block;
                position: absolute;
            }
        }
    }

    &:hover {
        .info {
            display: block;
        }
    }

    &:hover,
    &.selected {
        background: #41b57a12;
    }

    &.selected {
        border: 1px solid #58b57a !important;
    }

    &.selected:not(.delivery-date-custom) {
        i.icon-check {
            display: block;
        }
    }

    &.selected.delivery-date-custom.selected-date {
        i.icon-check {
            display: block;
        }
    }
}


body > .loading {
    z-index:9999 !important;
}


body > .loading-inline {
    position: fixed !important;
}

.loading {
	position: fixed;
	top:0px;
	left:0px;
	right:0px;
	bottom:0px;
	z-index: 48;
	background: #79797942;
	backdrop-filter: blur(1px);
	display: flex;
    justify-content: center;
    align-items: center;

	&.loading-inline {
		flex-direction: column;
		position: absolute;
	}
}

.loading-search {
    .fa-spin {
        color: theme-color('secondary');
        position: absolute;
        top: 25%;
    	right: 45%;
    }
}

.badge-200 {
    border: 1px solid #9b9b9b;
    background-color: #fdfdfd;
    color: #7a7a7a;
}
.badge-210 {
    border: 1px solid #c59427;
    background-color: #ffeec8;
    color: #d19529;
}
.badge-220 {
    border: 1px solid #c54343;
    background-color: #ffc9c9;
    color: #d14545;
}
.badge-230 {
    border: 1px solid #45a172;
    background-color: #b3efc3;
    color: #389b44;
}



.badge-0 {
    border: 1px solid #9b9b9b;
    background-color: #fdfdfd;
    color: #7a7a7a;
}
.badge-15 {
    border: 1px solid #bb001a;
    background-color: #ff9e9e;
    color: #520505;
}


.badge-20 {
    border: 1px solid #000000;
    background-color: #e2e46d;
    color: #8d0000;
    font-weight: bold;
    border-style: dashed;
}
.badge-21 {
    border: 1px solid #626900;
    background-color: #e2e46d;
    color: #000000;
    font-weight: bold;
    border-style: dashed;
}

.badge-22 {
    border: 1px solid #7e0000;
    background-color: #e2e46d;
    color: #7e0000;
    font-weight: bold;
    border-style: dashed;
}
.badge-23 {
    border: 1px solid #0b8600;
    background-color: #e2e46d;
    color: #085f00;
    font-weight: bold;
    border-style: dashed;
}


.badge-30 {
    border: 1px solid #999999;
    background-color: #e7e7e7;
    color: #999999;
}
.badge-50 {
    border: 1px solid #6f89af;
    background-color: #dfecff;
    color: #6f89af !important;
}
.badge-70 {
    border: 1px solid #c59427;
    background-color: #f7e9cb;
    color: #dba42a;
}
.badge-110 {
    border: 1px solid #ac54c9;
    background-color: #efddf5;
    color: #bd75d5;
}
.badge-130 {
    border: 1px solid #5dc992;
    background-color: #cef5e1;
    color: #5dc992;
}
.badge-150 {
    border: 1px solid #4ea579;
    background-color: #c7e7d7;
    color: #41b57a;
}

.badge-160 {
    border: 1px solid #585858;
    background-color: #d1d1d1;
    color: #2c2c2c;
}

